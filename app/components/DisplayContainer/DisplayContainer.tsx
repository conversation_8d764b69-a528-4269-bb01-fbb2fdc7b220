'use client'

import React, { useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { LayoutConfig, DeviceConfig, transformConfigToCSSString } from './DisplayConfig'
import { globalImageSources } from '../../config/ImageSources'
import { useCalculatedDimensions, useIsMobile } from '../../hooks/useAppState'
import { useZoom, useDragHandlePosition } from '../../features/viewDimensions/utils/重构/状态管理'
import {
    useImageStore,
    getImageForDevice,
    getCurrentSelectedDevice,
} from '../../ImageMange/imageMangeIndex'
import {
    useMagicBackgroundStore,
    MagicBackgroundType,
    MagicSolidBackgroundValue,
    MagicGradientBackgroundValue,
    MagicMeshBackgroundValue,
    MagicImageBackgroundValue,
} from '../../hooks/useMagicBackgroundStore'
import { useBackgroundStore, BackgroundTypeEnum } from '../../hooks/useBackgroundStore'
import { useCustomImageStore } from '../../hooks/useCustomImageStore'
import { useSceneStore, SceneTabType, ShadowLayerType } from '../../hooks/useSceneStore'
import { useMockupStore, MockupStyleEnum } from '../../hooks/useMockupStore'
// 1. 导入 ShadowTypeEnum
import { useMockupShadowStore, ShadowTypeEnum } from '../../hooks/useMockupShadowStore'
import { getDeviceSizeConfig } from '../DragPad/dragPadConfig'
import { HTML_ACCEPT_ATTRIBUTES } from '../../ImageMange/imageConfig'
import { 
    calculatePercentageTransform, 
    DRAG_HANDLE_CONFIG, 
} from '../../config/dragHandleConfig'

/**
 * @interface DisplayContainerProps
 * @description DisplayContainer 组件的属性
 * @property {LayoutConfig} layoutConfig - 当前要渲染的布局配置。
 * @property {number} [canvasWidth] - 画布的宽度（可选）。如果未提供，将自动从状态管理中获取。
 * @property {number} [canvasHeight] - 画布的高度（可选）。如果未提供，将自动从状态管理中获取。
 * @property {(isActive: boolean) => void} [setIsActiveMediaPickerModal] - 设置媒体选择器弹窗状态的函数（可选）
 */
interface DisplayContainerProps {
    layoutConfig: LayoutConfig
    canvasWidth?: number
    canvasHeight?: number
    isUseCanvasView?: boolean
    setIsActiveMediaPickerModal?: (isActive: boolean) => void
}

/**
 * @component DisplayContainer
 * @description 一个高度独立的、数据驱动的设备布局渲染引擎。
 * 它可以根据传入的布局配置（LayoutConfig）在指定的画布尺寸上渲染出单设备或多设备的场景。
 * 它能自动从全局配置中获取并应用图片，并能独立获取渲染尺寸，同时内置了健壮的错误处理机制。
 */
const DisplayContainer = ({
    layoutConfig,
    canvasWidth,
    canvasHeight,
    isUseCanvasView,
    setIsActiveMediaPickerModal,
}: DisplayContainerProps) => {
    // 1. --- 状态管理 ---
    // 管理每个设备的 hover 状态，使用设备索引作为键
    const [deviceHoverStates, setDeviceHoverStates] = useState<Record<number, boolean>>({})

    // 2. --- 图片状态管理 ---
    // 从图片管理系统中获取状态和方法
    const {
        addImages,
        setImageDevice,
        setDragState,
        dragState,
        getNextAvailableDevice,
        setCurrentSelectedDevice,
    } = useImageStore()

    // 3. --- 尺寸管理 ---
    // 内部通过 hook 获取全局计算的尺寸和移动端状态。
    const { calculatedWidth, calculatedHeight } = useCalculatedDimensions()
    const isMobile = useIsMobile()

    // 4. --- 视图控制状态 ---
    // 获取当前缩放值，用于Canvas视图模式的缩放控制
    const zoom = useZoom()
    const dragHandlePosition = useDragHandlePosition()

    // 计算组件变换样式的函数
    // Canvas模式：用zoom替换componentTransform的scale值，保持其他属性不变
    // 普通模式：使用原始的componentTransform
    const getTransformStyle = (componentTransform?: typeof layoutConfig.componentTransform): string => {
        if (!componentTransform) {
            return ''
        }
        
        if (isUseCanvasView) {
            // Canvas视图模式：手动构建transform，用zoom值替换scale
            const transformParts: string[] = []
            
            // 1. 透视变换（如果存在）
            if (componentTransform.perspective !== undefined) {
                transformParts.push(`perspective(${componentTransform.perspective}em)`)
            }
            
            // 2. 平移变换（保持componentTransform中的translate）
            if (componentTransform.translate) {
                transformParts.push(`translate(${componentTransform.translate.x}%, ${componentTransform.translate.y}%)`)
            }
            
            // 3. 缩放变换：用zoom/100替换componentTransform的scale
            transformParts.push(`scale(${zoom / 100})`)
            
            // 4. 旋转变换（保持componentTransform中的rotate）
            if (componentTransform.rotate) {
                transformParts.push(`rotateX(${componentTransform.rotate.x}deg)`)
                transformParts.push(`rotateY(${componentTransform.rotate.y}deg)`)
                transformParts.push(`rotateZ(${componentTransform.rotate.z}deg)`)
            }
            
            // 5. 倾斜变换（如果存在，保持componentTransform中的skew）
            if (componentTransform.skew) {
                transformParts.push(`skewX(${componentTransform.skew.x}deg)`)
                transformParts.push(`skewY(${componentTransform.skew.y}deg)`)
            }
            
            return transformParts.join(' ')
        }
        
        // 普通模式：返回原始变换
        return transformConfigToCSSString(componentTransform)
    }

    // 关键设计点：优先使用从 props 传入的尺寸，否则使用内部通过 hook 获取的全局尺寸。
    // 这种设计使得组件具备高度的灵活性：
    // - 在主画布(CanvasContainer)中调用时，不传入尺寸，组件会自动采用全局尺寸。
    // - 在预览(MobileMockup_Layout)中调用时，传入固定的尺寸（如 117x88），组件会采用该固定尺寸。
    const actualWidth = canvasWidth ?? calculatedWidth
    const actualHeight = canvasHeight ?? calculatedHeight

    // 4. --- 拖拽功能 (必须在条件检查之前调用hooks) ---
    // 配置全局拖拽区域
    const dropzone = useDropzone({
        accept: {
            'image/*': ['.png', '.jpg', '.jpeg', '.webp'],
        },
        onDrop: async acceptedFiles => {
            if (acceptedFiles.length > 0) {
                await addImages(acceptedFiles)
                // 自动分配到下一个可用设备
                const images = useImageStore.getState().images
                const newImage = images[images.length - 1]
                if (newImage) {
                    const deviceIndex = getNextAvailableDevice()
                    setImageDevice(newImage.id, deviceIndex)
                }
            }
            setDragState({ dragOverTarget: null, isDragging: false })
        },
        onDragEnter: () => {
            setDragState({
                dragOverTarget: 'display',
                isDragging: true,
            })
        },
        onDragLeave: () => {
            setDragState({ dragOverTarget: null })
        },
        noClick: true, // 禁用默认点击，我们处理设备的点击
    })

    // 为每个设备创建独立的dropzone
    const deviceDropzone0 = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: async acceptedFiles => {
            if (acceptedFiles.length > 0) {
                // 🔥 修复关键：临时保存并清除当前选中设备，避免自动绑定冲突
                const originalSelectedDevice = useImageStore.getState().currentSelectedDevice
                setCurrentSelectedDevice(null)

                // 上传图片（此时不会自动绑定到任何设备）
                await addImages(acceptedFiles)

                // 手动绑定到拖拽目标设备（设备0）
                const images = useImageStore.getState().images
                const newImage = images[images.length - 1]
                if (newImage) {
                    setImageDevice(newImage.id, 0)
                }

                // 恢复原来的选中设备
                setCurrentSelectedDevice(originalSelectedDevice)
            }
            setDragState({ dragOverTarget: null, isDragging: false })
        },
        onDragEnter: () => setDragState({ dragOverTarget: 'device-0', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true,
    })

    const deviceDropzone1 = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: async acceptedFiles => {
            if (acceptedFiles.length > 0) {
                // 🔥 修复关键：临时保存并清除当前选中设备，避免自动绑定冲突
                const originalSelectedDevice = useImageStore.getState().currentSelectedDevice
                setCurrentSelectedDevice(null)

                // 上传图片（此时不会自动绑定到任何设备）
                await addImages(acceptedFiles)

                // 手动绑定到拖拽目标设备（设备1）
                const images = useImageStore.getState().images
                const newImage = images[images.length - 1]
                if (newImage) {
                    setImageDevice(newImage.id, 1)
                }

                // 恢复原来的选中设备
                setCurrentSelectedDevice(originalSelectedDevice)
            }
            setDragState({ dragOverTarget: null, isDragging: false })
        },
        onDragEnter: () => setDragState({ dragOverTarget: 'device-1', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true,
    })

    const deviceDropzone2 = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: async acceptedFiles => {
            if (acceptedFiles.length > 0) {
                // 🔥 修复关键：临时保存并清除当前选中设备，避免自动绑定冲突
                const originalSelectedDevice = useImageStore.getState().currentSelectedDevice
                setCurrentSelectedDevice(null)

                // 上传图片（此时不会自动绑定到任何设备）
                await addImages(acceptedFiles)

                // 手动绑定到拖拽目标设备（设备2）
                const images = useImageStore.getState().images
                const newImage = images[images.length - 1]
                if (newImage) {
                    setImageDevice(newImage.id, 2)
                }

                // 恢复原来的选中设备
                setCurrentSelectedDevice(originalSelectedDevice)
            }
            setDragState({ dragOverTarget: null, isDragging: false })
        },
        onDragEnter: () => setDragState({ dragOverTarget: 'device-2', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true,
    })

    const deviceDropzones = [deviceDropzone0, deviceDropzone1, deviceDropzone2]

    // 5. --- 健壮性与错误处理 ---
    // 🔥 严格检查：存储尺寸有效性状态，但不中断hooks调用流程
    const isValidDimensions = !!(actualWidth && actualHeight && actualWidth > 0 && actualHeight > 0)

    if (!isValidDimensions) {
        // 在控制台打印详细的错误日志，方便开发者追溯问题。
        console.error('🚨 DisplayContainer 初始化失败 - 尺寸缺失:', {
            propWidth: canvasWidth,
            propHeight: canvasHeight,
            calculatedWidth,
            calculatedHeight,
            timestamp: new Date().toISOString(),
        })
    }

    // 6. --- 数据处理与图片绑定集成 ---

    // 从传入的布局配置中解构出核心参数
    const { componentTransform, devices, aspectRatio } = layoutConfig

    /**
     * 🔥 核心数据转换：将布局设备配置与图片绑定状态合并
     *
     * 实现原理：
     * 1. 遍历布局配置中的每个设备
     * 2. 通过 getImageForDevice 获取设备绑定的第一张图片
     * 3. 将图片预览URL附加到设备配置中
     * 4. 保持设备配置的不可变性
     *
     * 向后兼容性：
     * - getImageForDevice 返回设备绑定的第一张图片
     * - 支持一对多绑定系统，但渲染时只显示第一张
     * - 保持原有的 imageSource 字段结构
     *
     * 数据流：
     * 状态管理图片数据 → 设备绑定查询 → 布局配置增强 → 设备渲染
     */
    const devicesWithImages = devices.map((device, index) => {
        // 查询设备绑定的图片（返回第一张绑定的图片，保持向后兼容）
        const deviceImage = getImageForDevice(index)

        if (deviceImage) {
            // 将图片预览URL集成到设备配置中
            return {
                ...device,
                imageSource: deviceImage.preview,
            }
        }

        // 设备未绑定图片时保持原配置
        return device
    })

    // 5. --- 动态样式计算 ---
    // 根据设备数量决定容器的尺寸约束策略，以达到最佳视觉效果。
    let containerWidth: string
    let containerHeight: string
    if (devices.length === 3) {
        // 对于三设备布局，我们限制其总宽度为父容器的75%，高度则自适应，以防止在横向空间不足时被过度压缩。
        containerWidth = '75%'
        containerHeight = 'auto'
    } else {
        // 对于单设备或双设备布局，我们限制其总高度为父容器的75%，宽度自适应，这在纵向展示上效果更好。
        containerWidth = 'auto'
        containerHeight = '75%'
    }

    // 6. --- 事件处理函数 ---
    /**
     * @function handleDeviceMouseEnter
     * @description 处理设备鼠标进入事件，设置对应设备的 hover 状态为 true
     * @param {number} deviceIndex - 设备在数组中的索引
     */
    const handleDeviceMouseEnter = (deviceIndex: number) => {
        setDeviceHoverStates(prev => ({
            ...prev,
            [deviceIndex]: true,
        }))
    }

    /**
     * @function handleDeviceMouseLeave
     * @description 处理设备鼠标离开事件，设置对应设备的 hover 状态为 false
     * @param {number} deviceIndex - 设备在数组中的索引
     */
    const handleDeviceMouseLeave = (deviceIndex: number) => {
        setDeviceHoverStates(prev => ({
            ...prev,
            [deviceIndex]: false,
        }))
    }

    /**
     * @function handleDeviceClick
     * @description 处理设备点击事件，设置当前选中的设备并弹出媒体选择器
     * @param {number} deviceIndex - 设备在数组中的索引
     */
    const handleDeviceClick = (deviceIndex: number) => {
        // 设置当前选中的设备
        setCurrentSelectedDevice(deviceIndex)

        // 弹出媒体选择器
        if (setIsActiveMediaPickerModal) {
            setIsActiveMediaPickerModal(true)
        }

        console.log(`点击了设备 ${deviceIndex}`)
    }

    // 从全局store获取样式状态 - 必须在组件顶层调用
    const { styles, activeStyle } = useMockupStore()

    /**
     * @function renderDevice
     * @description 渲染单个设备组件 - 核心设备展示和交互逻辑
     *
     * 功能职责：
     * 1. 设备外观渲染：基于配置渲染设备外观和变换效果
     * 2. 图片内容显示：动态显示绑定的图片或空状态
     * 3. 交互状态管理：处理鼠标悬停、拖拽、点击等交互
     * 4. 多状态UI：根据不同状态显示对应的视觉反馈
     *
     * 交互状态系统：
     * - 默认状态：显示 "Drop or Paste" 提示
     * - 悬停状态：显示 "Select Media" 提示
     * - 拖拽状态：显示拖拽投放提示
     * - 有图状态：显示实际图片内容
     *
     * 技术实现：
     * - 使用 SVG mask 实现设备屏幕形状
     * - 多层状态控制的视觉反馈
     * - 拖拽区域与点击区域的事件处理
     *
     * @param {DeviceConfig} device 设备配置对象，包含变换、样式等信息
     * @param {number} index 设备索引，用于状态管理和事件处理
     * @returns {JSX.Element} 完整的设备JSX元素
     */
    const renderDevice = (device: DeviceConfig, index: number) => {
        // 判断设备是否已绑定图片 - 决定显示模式
        const showImage = !!device.imageSource

        // 获取设备的鼠标悬停状态 - 用于交互反馈
        const isHovered = deviceHoverStates[index] || false

        // 获取当前激活的完整样式对象
        const activeStyleObj = styles.find(style => style.label === activeStyle)

        // 健壮性处理：如果未找到样式，使用默认值
        const currentStyleLabel = activeStyleObj?.label ?? 'display'
        const currentStyleSrc =
            activeStyleObj?.deviceSrc ?? '/mockups/iPhone 15 Plus/portrait/black.png'

        return (
            <div
                key={index}
                className={`devices iphone-15-plus ${currentStyleLabel} ${currentStyleLabel !== MockupStyleEnum.Display && 'phone-shadow-config'}`}
                style={{
                    position: 'absolute',
                    willChange: 'transform',
                    width: '100%',
                    height: 'auto',
                    aspectRatio: '430 / 932',
                    transform: transformConfigToCSSString(device.transform),
                    filter: device.filter ?? 'none',
                    transition: 'transform 0.125s linear',
                    transformOrigin: 'center center',
                    zIndex: device.zIndex ?? 'unset',
                }}
                onMouseEnter={() => handleDeviceMouseEnter(index)}
                onMouseLeave={() => handleDeviceMouseLeave(index)}
            >
                <div className='item'>
                    <div className='item-container'>
                        {/* 设备图片 */}
                        {currentStyleLabel !== 'display' && (
                            <div className='device-asset is-portrait'>
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    className='device-asset-image'
                                    alt='device'
                                    src={currentStyleSrc}
                                />
                            </div>
                        )}
                        <div
                            {...deviceDropzones[index].getRootProps()}
                            className='drop-wrapper'
                            style={{
                                aspectRatio: '430 / 932',
                                maskImage: 'url("/mockups/iPhone 15 Plus/portrait/display.svg")',
                                maskSize: '100% 100%',
                                width: '100%',
                                height: '100%',
                            }}
                            onClick={e => {
                                e.stopPropagation()
                                handleDeviceClick(index)
                            }}
                        >
                            <div
                                className={`dropzone ${!showImage ? 'bg-panel-dim' : ''} ${
                                    isUseCanvasView && getCurrentSelectedDevice() === index
                                        ? 'active-state'
                                        : ''
                                }`}
                                style={{
                                    overflow: 'hidden',
                                }}
                            >
                                <div className='file-drop' style={{ cursor: 'pointer' }} />

                                {/* // 设备已绑定图片时显示的图片 */}
                                {showImage && (
                                    <>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            className='dropped-image'
                                            src={device.imageSource}
                                            style={{ objectFit: 'cover' }}
                                        />
                                    </>
                                )}

                                {/* 设备未绑定图片时显示的提示 */}
                                {!showImage &&
                                    !isHovered &&
                                    dragState.dragOverTarget !== `device-${index}` && (
                                    <>
                                        <div
                                            className='empty-state start-state'
                                            style={{
                                                opacity: isHovered ? '0' : '1',
                                                // transition: 'opacity 0.2s ease-in-out',
                                                // pointerEvents: isHovered ? 'none' : 'auto',
                                            }}
                                        >
                                            <div className='icons'>
                                                <div className='icon'>
                                                    <svg
                                                        viewBox='0 0 24 24'
                                                        xmlns='http://www.w3.org/2000/svg'
                                                    >
                                                        <path
                                                            d='M12.97 11.16h3.88c.46 0 .83.37.83.83s-.38.83-.84.83h-3.89v3.88c0 .46-.38.83-.84.83-.47 0-.84-.38-.84-.84V12.8H7.38c-.47 0-.84-.38-.84-.84 0-.47.37-.84.83-.84h3.88V7.23a.83.83 0 1 1 1.66-.01v3.88ZM5.06 4.92c-3.91 3.9-3.91 10.23 0 14.14 3.9 3.9 10.23 3.9 14.14 0 3.9-3.91 3.9-10.24 0-14.15a10 10 0 0 0-14.15 0Z'
                                                            fill='currentColor'
                                                            fillRule='evenodd'
                                                        />
                                                    </svg>
                                                </div>
                                                <div className='image-icon'>
                                                    <svg
                                                        viewBox='0 0 24 24'
                                                        xmlns='http://www.w3.org/2000/svg'
                                                    >
                                                        <path
                                                            d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                                            fill='currentColor'
                                                        />
                                                    </svg>
                                                </div>
                                                <div className='video-icon'>
                                                    <svg
                                                        viewBox='0 0 24 24'
                                                        xmlns='http://www.w3.org/2000/svg'
                                                    >
                                                        <path
                                                            d='M4.599 18.5h8.628c1.616 0 2.6-.928 2.6-2.517V8.016c0-1.589-.891-2.516-2.497-2.516H4.599C3.077 5.5 2 6.427 2 8.016v7.967c0 1.589.983 2.517 2.599 2.517m12.429-4.137 2.962 2.557c.28.245.622.398.912.398.663 0 1.098-.479 1.098-1.152V7.833c0-.673-.435-1.152-1.098-1.152-.29 0-.632.153-.912.398l-2.962 2.557z'
                                                            fill='currentColor'
                                                        />
                                                    </svg>
                                                </div>
                                            </div>
                                            <span className='title'>Drop or Paste</span>
                                            <span className='subtitle'>Images & Videos</span>
                                        </div>
                                    </>
                                )}
                                <input
                                    className='d-none'
                                    accept={HTML_ACCEPT_ATTRIBUTES.STANDARD_IMAGES}
                                    type='file'
                                />
                                {/* 鼠标悬浮时显示的提示 */}
                                {isHovered && !dragState.dragOverTarget && (
                                    <>
                                        <div
                                            className='empty-state select-state'
                                            style={{
                                                opacity:
                                                    isHovered && !dragState.dragOverTarget
                                                        ? '1'
                                                        : '0',
                                                // transition: 'opacity 0.2s ease-in-out',
                                                // pointerEvents:
                                                //     isHovered && !dragState.dragOverTarget
                                                //         ? 'auto'
                                                //         : 'none',
                                            }}
                                        >
                                            <div className='icon'>
                                                <svg
                                                    viewBox='0 0 24 24'
                                                    xmlns='http://www.w3.org/2000/svg'
                                                >
                                                    <g fill='currentColor' fillRule='evenodd'>
                                                        <path d='M10.33 6.5H6.95c-2.97 0-4.96 2.08-4.96 5.17v7.14c0 3.09 1.99 5.17 4.95 5.17h7.58c2.96 0 4.95-2.09 4.95-5.18v-5.72c-2.43.78-5.2.21-7.119-1.71a7.02 7.02 0 0 1-2.06-4.91Zm3.31 9.73c.19-.27.91-1.09 1.85-.5.59.36 1.09.86 1.63 1.4.2.2.35.43.44.68.29.75.14 1.65-.18 2.39-.38.88-1.09 1.55-1.98 1.84-.4.13-.82.18-1.24.18H6.78c-.74 0-1.39-.18-1.92-.51-.34-.21-.4-.69-.15-.99l1.23-1.56c.78-1 1.31-1.29 1.9-1.04.23.1.47.26.72.42.65.44 1.56 1.05 2.77.39.82-.47 1.29-1.26 1.71-1.94v-.02c.02-.05.05-.1.08-.15.13-.24.27-.46.43-.67Zm-8.15-4.05c0-1.21.98-2.19 2.18-2.19s2.18.98 2.18 2.18-.99 2.18-2.19 2.18c-1.21 0-2.19-.99-2.19-2.19Z' />
                                                        <path d='M18.2 5.5h2.32c.27 0 .5.22.5.49s-.23.49-.5.49h-2.33V8.8a.5.5 0 0 1-1-.01V6.46h-2.33c-.28 0-.5-.23-.5-.5 0-.28.22-.5.49-.5h2.32V3.13c0-.28.22-.5.49-.5s.49.22.49.49v2.32Zm-4.75-3.75a5.98 5.98 0 0 0 0 8.48 6 6 0 1 0 8.48-8.49 5.993 5.993 0 0 0-8.49 0Z' />
                                                    </g>
                                                </svg>
                                            </div>
                                            <span className='title'>Select Media</span>
                                            <span className='subtitle'>Open Media Picker</span>
                                        </div>
                                    </>
                                )}
                                {/* 用户拖拽图片放在设备上时显示的提示 */}
                                <div
                                    className='empty-state dropping-state'
                                    style={{
                                        opacity:
                                            isUseCanvasView &&
                                            dragState.dragOverTarget === `device-${index}`
                                                ? '1'
                                                : '0',
                                    }}
                                >
                                    <svg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                                        <path
                                            d='M22.141 7.756q.858 1.99.859 4.25 0 2.248-.859 4.237a11.1 11.1 0 0 1-2.386 3.508 11.4 11.4 0 0 1-3.516 2.384q-1.99.864-4.239.865-2.25 0-4.24-.865a11.4 11.4 0 0 1-3.516-2.378 11 11 0 0 1-2.386-3.5A10.6 10.6 0 0 1 1 12.02a10.6 10.6 0 0 1 .858-4.25 11.2 11.2 0 0 1 2.386-3.515 11.3 11.3 0 0 1 3.516-2.39A10.5 10.5 0 0 1 12 .999q2.249 0 4.239.866a11.4 11.4 0 0 1 3.516 2.383 11.1 11.1 0 0 1 2.386 3.508M12.736 6.68q-.369-.368-.75-.368-.368 0-.736.368l-3.64 3.61q-.245.245-.245.654a.886.886 0 0 0 .913.913.97.97 0 0 0 .668-.273l1.159-1.171 1.049-1.308-.109 2.506v5.163q0 .409.266.674a.91.91 0 0 0 .675.266.91.91 0 0 0 .675-.266.91.91 0 0 0 .265-.674v-5.163l-.095-2.52 1.05 1.322 1.145 1.171a.9.9 0 0 0 .667.273.92.92 0 0 0 .662-.259.88.88 0 0 0 .265-.654.89.89 0 0 0-.259-.654z'
                                            fill='currentColor'
                                            fillRule='evenodd'
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        {mockupShadowType !== ShadowTypeEnum.None && (
                            <div className='shadow'>
                                <div
                                    className='shadow-layer'
                                    style={{
                                        boxShadow: dynamicBoxShadow,
                                        willChange: '',
                                    }}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        )
    }

    // 7. --- 背景状态管理 ---
    const { backgroundType, color, unsplashImage } = useBackgroundStore()
    const { selectedBackground } = useMagicBackgroundStore()
    const { image: customImage } = useCustomImageStore()

    // 8. --- 阴影状态管理 ---
    const { sceneType, shadowType, shadowOpacity, shadowLayer } = useSceneStore()

    // 使用新的全局阴影状态管理
    const { shadowType: mockupShadowType, opacity, position } = useMockupShadowStore()
    const currentShadowOpacoty = opacity / 100

    // 这段代码应该放在 DisplayContainer 组件内部，return 语句之前

    // 1. --- 从 store 和 hooks 获取必要的状态 ---
    // 从 store 获取 position 和 opacity，并重命名 opacity 以避免变量名冲突
    const { opacity: shadowOpacityValue } = useMockupShadowStore()

    // 2. --- 定义所有阴影类型的统一配置 (最终自适应模型) ---
    const SHADOW_CONFIG = {
        [ShadowTypeEnum.Spread]: {
            layers: [
                { maxOffset: 2.75, baseBlur: 1.42 },
                { maxOffset: 8.25, baseBlur: 4.27 },
                { maxOffset: 16.5, baseBlur: 8.53 },
                { maxOffset: 27.5, baseBlur: 14.22 },
            ],
            stretchFactor: 1.64, // Custom stretch factor for Spread
            spread: -2.5,
            offsetMultiplier: 1.0,
        },
        [ShadowTypeEnum.Hug]: {
            layers: [
                { maxOffset: 2.75, baseBlur: 1.65 },
                { maxOffset: 8.25, baseBlur: 4.95 },
            ],
            stretchFactor: Math.sqrt(2), // sqrt(2) for perfect diagonal stretch
            spread: 0,
            offsetMultiplier: 0.5,
        },
        [ShadowTypeEnum.Adaptive]: null, // Future-proofing
        [ShadowTypeEnum.None]: null,
    }

    // 3. --- 使用 useMemo 实现响应式、高性能的阴影计算 ---
    const dynamicBoxShadow = React.useMemo(() => {
        const config = SHADOW_CONFIG[mockupShadowType]

        if (!config) {
            return ''
        }

        const sizeConfig = getDeviceSizeConfig(isMobile)
        const { containerSize, handleSize } = sizeConfig
        const lightSourceCenterX = position.x + handleSize / 2
        const lightSourceCenterY = position.y + handleSize / 2
        const containerCenterX = containerSize / 2
        const containerCenterY = containerSize / 2
        const maxTravelFromCenter = (containerSize - handleSize) / 2

        const normalizedX =
            maxTravelFromCenter > 0
                ? (lightSourceCenterX - containerCenterX) / maxTravelFromCenter
                : 0
        const normalizedY =
            maxTravelFromCenter > 0
                ? (lightSourceCenterY - containerCenterY) / maxTravelFromCenter
                : 0

        const shadowColor = `rgba(0, 0, 0, ${shadowOpacityValue / 100})`

        // --- Universal Shadow Calculation Loop ---
        const shadowStrings = config.layers.map(layer => {
            const offsetX = -normalizedX * layer.maxOffset * config.offsetMultiplier
            const offsetY = -normalizedY * layer.maxOffset * config.offsetMultiplier

            // Universal "Stretching" Blur Model
            const stretch =
                1 + (config.stretchFactor - 1) * Math.abs(normalizedX) * Math.abs(normalizedY)
            const blur = (layer.baseBlur || 0) * stretch

            const spread = config.spread

            return `${offsetX.toFixed(3)}em ${offsetY.toFixed(3)}em ${blur.toFixed(3)}em ${spread.toFixed(
                3,
            )}em ${shadowColor}`
        })

        return shadowStrings.join(', ')
    }, [position, shadowOpacityValue, isMobile, mockupShadowType]) // 依赖项：当这些值变化时，重新计算

    /**
     * @description 根据背景类型渲染对应的背景组件
     * @returns {React.ReactElement} 渲染的背景组件
     */
    const renderBackground = (): React.ReactElement => {
        // 根据 useBackgroundStore 的 backgroundType 统一调度背景渲染
        if (backgroundType === BackgroundTypeEnum.TRANSPARENT) {
            // 透明背景：返回空片段
            return <></>
        } else if (backgroundType === BackgroundTypeEnum.COLOR) {
            // 纯色背景：从 useBackgroundStore 获取颜色值
            return <div className='frame-background' style={{ background: color, opacity: 1 }} />
        } else if (backgroundType === BackgroundTypeEnum.IMAGE) {
            // 图片背景：从 useCustomImageStore 获取用户上传的图片
            if (customImage?.previewUrl) {
                return (
                    <div style={{ opacity: 1 }}>
                        <img
                            crossOrigin='anonymous'
                            loading='eager'
                            decoding='async'
                            className='frame-background'
                            alt='background'
                            src={customImage.previewUrl}
                        />
                    </div>
                )
            }
            // 如果没有自定义图片，返回空
            return <></>
        } else if (backgroundType === BackgroundTypeEnum.UNSPLASH) {
            // Unsplash 背景：从 useBackgroundStore 获取图片信息
            if (unsplashImage?.url) {
                return (
                    <div style={{ opacity: 1 }}>
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            className='frame-background'
                            alt='unsplash background'
                            src={unsplashImage.url}
                        />
                    </div>
                )
            }
            // 如果没有 Unsplash 图片，返回空
            return <></>
        } else if (backgroundType === BackgroundTypeEnum.MAGIC) {
            // 魔法背景：委托给 useMagicBackgroundStore 管理具体样式
            if (!selectedBackground) return <></>

            const { type, value } = selectedBackground

            /**
             * 魔法背景子类型渲染逻辑
             * 保持原有的魔法背景渲染能力，完全由 useMagicBackgroundStore 控制
             */
            if (type === MagicBackgroundType.SOLID) {
                // 纯色魔法背景
                const solidValue = value as MagicSolidBackgroundValue
                return (
                    <div
                        className='frame-background'
                        style={{
                            background: solidValue.background,
                            opacity: 1,
                        }}
                    />
                )
            } else if (type === MagicBackgroundType.GRADIENT) {
                // 渐变魔法背景
                const gradientValue = value as MagicGradientBackgroundValue
                return (
                    <div
                        className='frame-background'
                        style={{
                            background: gradientValue.background,
                            opacity: 1,
                        }}
                    />
                )
            } else if (type === MagicBackgroundType.MESH) {
                // 网格魔法背景
                const meshValue = value as MagicMeshBackgroundValue
                return (
                    <div style={{ width: '100%', height: '100%', opacity: 1 }}>
                        <div className='mesh-display mesh-5-1'>
                            <div
                                style={{
                                    backgroundColor: meshValue.backgroundColor,
                                    backgroundImage: meshValue.backgroundImage,
                                }}
                            />
                        </div>
                    </div>
                )
            } else if (type === MagicBackgroundType.IMAGE) {
                // 图片魔法背景
                const imageValue = value as MagicImageBackgroundValue
                return (
                    <div style={{ opacity: 1 }}>
                        <div
                            className='magic-image-display style-2'
                            style={{ background: imageValue.background }}
                        >
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                alt='magicImage'
                                src={imageValue.imageUrl}
                            />
                        </div>
                    </div>
                )
            } else {
                // 未知魔法背景类型，抛出错误
                throw new Error(`不支持的魔法背景类型: ${type}`)
            }
        } else {
            // 未知背景类型，返回空
            return <></>
        }
    }

    // 根据尺寸有效性条件渲染内容或错误提示
    if (!isValidDimensions) {
        return (
            <div
                className='frame preview-frame'
                style={{
                    width: 400,
                    height: 300,
                    backgroundColor: '#ff4444',
                    color: 'white',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '20px',
                    textAlign: 'center',
                }}
            >
                <h3 style={{ margin: '0 0 10px 0' }}>❌ 渲染失败</h3>
                <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
                    DisplayContainer 未能获取有效尺寸
                </p>
                <div style={{ fontSize: '12px', opacity: 0.8 }}>
                    <p>传入宽度: {canvasWidth || '未提供'}</p>
                    <p>传入高度: {canvasHeight || '未提供'}</p>
                    <p>计算宽度: {calculatedWidth || '未定义'}</p>
                    <p>计算高度: {calculatedHeight || '未定义'}</p>
                    <p>设备类型: {isMobile ? '移动端' : 'PC端'}</p>
                </div>
            </div>
        )
    }

    return (
        <div
            className='__remotion-player'
            style={{
                position: 'absolute',
                width: actualWidth,
                height: actualHeight,
                display: 'flex',
                transform: 'scale(1)',
                marginLeft: 0,
                marginTop: 0,
                overflow: 'hidden',
            }}
        >
            {/* 背景 */}
            <div className='frame-background-display'>
                <div className='frame-noise' style={{ opacity: 0 }} />
                {renderBackground()}
            </div>
            {/* 阴影 */}
            {/* 注意：当场景类型为阴影时才渲染 */}
            {sceneType === SceneTabType.SHADOW && (
                <div
                    className='shadow-scene-layer'
                    style={{
                        zIndex: shadowLayer === ShadowLayerType.OVERLAY ? 15 : 0,
                        opacity: shadowOpacity / 100,
                        filter: 'blur(0.4em)',
                    }}
                >
                    <img
                        crossOrigin='anonymous'
                        loading='lazy'
                        decoding='async'
                        alt='shadow'
                        src={shadowType.src}
                        style={{
                            position: 'absolute',
                            top: '-1.2em',
                            left: '-1.2em',
                            minWidth: 'calc(100% + 2.4em)',
                            maxWidth: 'calc(100% + 2.4em)',
                            height: 'calc(100% + 2.4em)',
                            zIndex: shadowLayer === ShadowLayerType.OVERLAY ? 15 : 0,
                        }}
                    />
                </div>
            )}

            {/* 设备 */}
            <div className='display-container-single'>
                <div
                    className='component'
                    style={{
                        willChange: 'transform',
                        position: 'absolute',
                        inset: 0,
                        transform: getTransformStyle(componentTransform),
                        transformOrigin: 'center center',
                        transition: 'transform 0.125s linear',
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            position: 'relative',
                            width: '100%',
                            height: '100%',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                position: 'relative',
                                width: containerWidth,
                                height: containerHeight,
                                aspectRatio: aspectRatio,
                            }}
                        >
                            {devicesWithImages.map(renderDevice)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default DisplayContainer
