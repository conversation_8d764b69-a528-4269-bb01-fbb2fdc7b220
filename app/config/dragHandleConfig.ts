/**
 * @file app/config/dragHandleConfig.ts
 * @description 拖拽手柄相关的统一配置文件
 * 所有与拖拽手柄位置、尺寸、变换相关的常量都集中在此文件中管理，
 * 避免硬编码参数分散在多个文件中导致的维护问题。
 */

// 导入布局配置相关的类型和数据
import { 
    ExtendedLayoutConfig, 
    TransformConfig, 
    LayoutType,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs, 
    tripleDeviceLayoutConfigs 
} from '../components/DisplayContainer/DisplayConfig'

/**
 * @interface DragHandlePosition
 * @description 拖拽手柄位置坐标接口
 */
export interface DragHandlePosition {
    x: number
    y: number
}

/**
 * @interface DragHandleDimensions
 * @description 拖拽手柄尺寸接口
 */
export interface DragHandleDimensions {
    width: number
    height: number
}

/**
 * @interface DragPadDimensions  
 * @description 拖拽区域尺寸接口
 */
export interface DragPadDimensions {
    width: number
    height: number
}

/**
 * @const DRAG_HANDLE_CONFIG
 * @description 拖拽手柄的统一配置对象
 * 包含所有与拖拽手柄相关的尺寸、位置、变换参数
 */
export const DRAG_HANDLE_CONFIG = {
    /**
     * 拖拽手柄的默认/初始/基准位置
     * 这个位置对应Canvas视图中的 translate(0%, 0%) 状态
     * 数值来源：通过实际测试和用户体验优化得出的最佳初始位置
     */
    BASE_POSITION: {
        x: 85.7143, // 基准X坐标（像素）- 约占拖拽区域宽度的 42.86%
        y: 63.4286, // 基准Y坐标（像素）- 约占拖拽区域高度的 42.86%
    } as DragHandlePosition,

    /**
     * 拖拽手柄元素自身的尺寸
     * 用于边界限制计算和碰撞检测
     */
    HANDLE_SIZE: {
        width: 28.5714,  // 拖拽手柄宽度（像素）
        height: 21.1429, // 拖拽手柄高度（像素）
    } as DragHandleDimensions,

    /**
     * 拖拽区域（drag-pad）的尺寸
     * 这是拖拽手柄可移动的有效范围
     * 数值来源：PcRightSlider.tsx 中的 drag-pad 容器尺寸
     */
    DRAG_PAD_SIZE: {
        width: 200,  // 拖拽区域宽度（像素）- 来自 208px 容器减去 4px*2 内边距
        height: 148, // 拖拽区域高度（像素）- 来自 156px 容器减去 4px*2 内边距
    } as DragPadDimensions,

    /**
     * 坐标转换相关配置
     * 用于将拖拽手柄的像素位移转换为Canvas视图的百分比变换
     */
    TRANSFORM: {
        /**
         * 是否应用反向逻辑
         * true: 拖拽手柄向右移动时，视图内容向左移动（推荐，符合用户直觉）
         * false: 拖拽手柄与视图内容同向移动
         */
        INVERT_DIRECTION: true,

        /**
         * 百分比精度
         * 控制 translate() 函数中百分比值的小数位数
         */
        PRECISION: 5,
    },

    /**
     * 默认的视图控制状态
     * 用于重置功能和初始化
     */
    DEFAULT_VIEW_CONTROL: {
        zoom: 100, // 默认缩放比例（100% = 1:1）
        dragHandlePosition: {
            x: 85.7143, // 使用基准位置作为默认值
            y: 63.4286,
        } as DragHandlePosition,
    },
} as const

/**
 * @function findLayoutConfig
 * @description 根据布局类型和ID查找对应的布局配置
 * @param {LayoutType} type - 布局类型（Single/Dual/Triple）
 * @param {number} id - 布局ID
 * @returns {ExtendedLayoutConfig | null} 匹配的布局配置，如果未找到则返回null
 */
export const findLayoutConfig = (type: LayoutType, id: number): ExtendedLayoutConfig | null => {
    let configArray: ExtendedLayoutConfig[]
    
    // 根据布局类型选择对应的配置数组
    if (type === LayoutType.Single) {
        configArray = singleDeviceLayoutConfigs
    } else if (type === LayoutType.Dual) {
        configArray = dualDeviceLayoutConfigs
    } else if (type === LayoutType.Triple) {
        configArray = tripleDeviceLayoutConfigs
    } else {
        console.warn(`未知的布局类型: ${type}`)
        return null
    }
    
    // 在配置数组中查找匹配的ID
    const config = configArray.find(layout => layout.id === id)
    
    if (!config) {
        console.warn(`未找到布局配置: type=${type}, id=${id}`)
        return null
    }
    
    return config
}

/**
 * @function componentTransformToViewControl
 * @description 将布局的componentTransform转换为viewControl格式
 * @param {TransformConfig} componentTransform - 布局的变换配置
 * @returns {object} viewControl格式的对象，包含zoom和dragHandlePosition
 */
export const componentTransformToViewControl = (componentTransform: TransformConfig) => {
    // 计算zoom：scale转换为百分比（例如：1.8 -> 180）
    const zoom = (componentTransform.scale || 1) * 100
    
    // 计算dragHandlePosition：从translate百分比反向计算拖拽手柄位置
    let dragHandlePosition = getDragHandleBasePosition()
    
    if (componentTransform.translate) {
        // 使用反向计算逻辑：
        // componentTransform.translate是Canvas的变换百分比
        // 需要转换为拖拽手柄在drag-pad中的像素位置
        
        const { x: translateX, y: translateY } = componentTransform.translate
        const padSize = getDragPadSize()
        const basePosition = getDragHandleBasePosition()
        
        // 反向计算：从百分比变换到拖拽手柄位置
        // 对于X轴：如果变换是向左(-x)，那么手柄应该在右边(+x)
        // 对于Y轴：如果变换是向上(-y)，那么手柄应该在下面(+y)
        const deltaX = DRAG_HANDLE_CONFIG.TRANSFORM.INVERT_DIRECTION 
            ? -(translateX / 100) * padSize.width
            : (translateX / 100) * padSize.width
        
        // 动态Y轴系数计算：根据translateY的绝对值确定合适的转换系数
        const calculateYCoefficient = (translateY: number): number => {
            const absY = Math.abs(translateY)
            
            if (absY <= 40) {
                return 4/7  // 基准系数，适用于 |translateY| ≤ 40
            } else if (absY >= 66) {
                return 20/49  // (5/7) * (4/7)，适用于 |translateY| ≥ 66
            } else {
                // 线性插值，处理 40 < |translateY| < 66 的情况
                const ratio = (absY - 40) / (66 - 40)  // 0 到 1 的插值比例
                return (4/7) * (1 - ratio * (1 - 5/7))  // 从 4/7 插值到 20/49
            }
        }
        
        const yCoefficient = calculateYCoefficient(translateY)
        const deltaY = -(translateY / 100) * padSize.height * yCoefficient
            
        dragHandlePosition = {
            x: Number((basePosition.x + deltaX).toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
            y: Number((basePosition.y + deltaY).toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
        }
    }
    
    return {
        zoom,
        dragHandlePosition,
    }
}

/**
 * @function getViewControlFromLayout  
 * @description 根据activeLayout动态获取viewControl配置
 * @param {LayoutType} type - 布局类型
 * @param {number} id - 布局ID
 * @returns {object} viewControl格式的配置对象，如果失败则返回默认配置
 */
export const getViewControlFromLayout = (type: LayoutType, id: number) => {
    // 查找布局配置
    const layoutConfig = findLayoutConfig(type, id)
    
    if (!layoutConfig) {
        console.warn(`无法获取布局配置，使用默认viewControl: type=${type}, id=${id}`)
        return getDefaultViewControl()
    }
    
    // 如果没有componentTransform，使用默认配置
    if (!layoutConfig.componentTransform) {
        console.warn(`布局无componentTransform，使用默认viewControl: type=${type}, id=${id}`)
        return getDefaultViewControl()
    }
    
    // 转换componentTransform为viewControl
    const viewControl = componentTransformToViewControl(layoutConfig.componentTransform)
    
    console.warn(`动态获取viewControl: type=${type}, id=${id}`, {
        componentTransform: layoutConfig.componentTransform,
        viewControl,
    })
    
    return viewControl
}

/**
 * @function getDragHandleBasePosition
 * @description 获取拖拽手柄的基准位置
 * 提供类型安全的访问方式
 * @returns {DragHandlePosition} 基准位置坐标
 */
export const getDragHandleBasePosition = (): DragHandlePosition => {
    return { ...DRAG_HANDLE_CONFIG.BASE_POSITION }
}

/**
 * @function getDragHandleSize
 * @description 获取拖拽手柄的尺寸
 * @returns {DragHandleDimensions} 手柄尺寸
 */
export const getDragHandleSize = (): DragHandleDimensions => {
    return { ...DRAG_HANDLE_CONFIG.HANDLE_SIZE }
}

/**
 * @function getDragPadSize
 * @description 获取拖拽区域的尺寸
 * @returns {DragPadDimensions} 拖拽区域尺寸
 */
export const getDragPadSize = (): DragPadDimensions => {
    return { ...DRAG_HANDLE_CONFIG.DRAG_PAD_SIZE }
}

/**
 * @function getDefaultViewControl
 * @description 获取默认的视图控制状态
 * 用于组件初始化和重置功能
 * @returns {object} 包含 zoom 和 dragHandlePosition 的默认状态对象
 */
export const getDefaultViewControl = () => {
    return {
        zoom: DRAG_HANDLE_CONFIG.DEFAULT_VIEW_CONTROL.zoom,
        dragHandlePosition: { ...DRAG_HANDLE_CONFIG.DEFAULT_VIEW_CONTROL.dragHandlePosition },
    }
}

/**
 * @function calculatePercentageTransform
 * @description 计算拖拽手柄位置对应的百分比变换
 * 将像素偏移转换为Canvas视图的translate百分比值
 * @param {DragHandlePosition} currentPosition - 当前拖拽手柄位置
 * @param {DragHandlePosition} basePosition - 基准位置（可选，默认使用配置中的基准位置）
 * @param {DragPadDimensions} padSize - 拖拽区域尺寸（可选，默认使用配置中的尺寸）
 * @returns {object} 包含 translateX 和 translateY 百分比值的对象
 */
export const calculatePercentageTransform = (
    currentPosition: DragHandlePosition,
    basePosition: DragHandlePosition = DRAG_HANDLE_CONFIG.BASE_POSITION,
    padSize: DragPadDimensions = DRAG_HANDLE_CONFIG.DRAG_PAD_SIZE,
): { translateX: number; translateY: number } => {
    // 计算相对于基准位置的偏移量（像素）
    const deltaX = currentPosition.x - basePosition.x
    const deltaY = currentPosition.y - basePosition.y
    
    // 动态Y轴系数计算（与componentTransformToViewControl中的逻辑一致）
    const calculateYCoefficientReverse = (deltaY: number): number => {
        // 根据deltaY推估对应的translateY绝对值，然后选择合适的系数
        const estimatedTranslateY = Math.abs((deltaY / padSize.height) * 100)
        
        if (estimatedTranslateY <= 40 * (4/7)) {
            return 4/7  // 基准系数
        } else if (estimatedTranslateY >= 66 * (20/49)) {
            return 20/49  // 大值系数
        } else {
            // 线性插值
            const lowerBound = 40 * (4/7)
            const upperBound = 66 * (20/49)
            const ratio = (estimatedTranslateY - lowerBound) / (upperBound - lowerBound)
            return (4/7) * (1 - ratio * (1 - 5/7))
        }
    }
    
    // 将像素偏移转换为百分比偏移
    const percentX = (deltaX / padSize.width) * 100
    const yCoefficient = calculateYCoefficientReverse(deltaY)
    const percentY = (deltaY / padSize.height) * 100 / yCoefficient
    
    // 应用反向逻辑（如果启用）
    const translateX = DRAG_HANDLE_CONFIG.TRANSFORM.INVERT_DIRECTION ? -percentX : percentX
    const translateY = DRAG_HANDLE_CONFIG.TRANSFORM.INVERT_DIRECTION ? -percentY : percentY
    
    return {
        translateX: Number(translateX.toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
        translateY: Number(translateY.toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
    }
}