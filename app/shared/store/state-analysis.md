# 🔍 状态管理现状分析报告

## 📊 状态源清单

基于项目代码分析，发现以下9个独立状态管理源：

### 🎨 核心应用状态组

#### 1. useAppStore
- **位置**: `app/features/viewDimensions/utils/重构/状态管理.ts`
- **职责**: 应用级核心状态管理
- **状态内容**:
  - 视图尺寸配置 (`currentViewDimensions`)
  - 导出设置 (`currentExportSettings`)
  - UI状态 (`ui.isMobile`)
  - 模态框状态 (`modal`)
- **依赖**: 无直接依赖，被其他状态源依赖
- **使用频率**: 🔴 极高 (核心状态)

#### 2. useAppState
- **位置**: `app/shared/hooks/useAppState.ts`
- **职责**: 应用状态选择器集合
- **状态内容**: 提供useAppStore的选择器hooks
- **依赖**: 直接依赖useAppStore
- **使用频率**: 🟡 中等 (辅助工具)

### 📸 内容管理状态组

#### 3. useImageStore
- **位置**: `app/features/imageManagement/core/imageMangeIndex.tsx`
- **职责**: 图片管理核心状态
- **状态内容**:
  - 图片列表 (`images`)
  - 选中状态 (`selectedImageId`)
  - 拖拽状态 (`dragState`)
  - 设备绑定 (`currentSelectedDevice`)
- **依赖**: 与useAppStore有隐式依赖关系
- **使用频率**: 🔴 极高 (核心业务)

#### 4. useCustomImageStore
- **位置**: `app/shared/hooks/useCustomImageStore.ts`
- **职责**: 自定义图片模态框状态
- **状态内容**:
  - 模态框内图片状态 (`image`)
  - 拖拽状态 (`isDragging`)
  - 错误状态 (`error`)
- **依赖**: 独立运行，与useImageStore有业务关联
- **使用频率**: 🟢 低 (特定功能)

#### 5. pasteUploadStore
- **位置**: `app/shared/hooks/pasteUploadStore.ts`
- **职责**: 粘贴上传功能状态
- **状态内容**: 粘贴上传的临时状态
- **依赖**: 与useImageStore有业务关联
- **使用频率**: 🟢 低 (特定功能)

### 🎭 视觉效果状态组

#### 6. useSceneStore
- **位置**: `app/shared/hooks/useSceneStore.ts`
- **职责**: 场景效果状态管理
- **状态内容**:
  - 场景类型 (`sceneType`)
  - 阴影样式 (`shadowType`)
  - 阴影透明度 (`shadowOpacity`)
  - 阴影层级 (`shadowLayer`)
- **依赖**: 与useAppStore有UI交互依赖
- **使用频率**: 🟡 中等

#### 7. useBackgroundStore
- **位置**: `app/shared/hooks/useBackgroundStore.ts`
- **职责**: 背景状态管理
- **状态内容**:
  - 背景类型 (`backgroundType`)
  - 颜色值 (`color`)
  - Unsplash图片 (`unsplashImage`)
  - 选择器可见性状态
- **依赖**: 与useMagicBackgroundStore有关联
- **使用频率**: 🟡 中等

#### 8. useMagicBackgroundStore
- **位置**: `app/shared/hooks/useMagicBackgroundStore.ts`
- **职责**: 魔法背景特效状态
- **状态内容**: 魔法背景的特定配置
- **依赖**: 与useBackgroundStore紧密关联
- **使用频率**: 🟢 低 (特定效果)

### 📱 设备模拟状态组

#### 9. useMockupStore
- **位置**: `app/shared/hooks/useMockupStore.ts`
- **职责**: 设备模拟状态
- **状态内容**: 设备模拟相关配置
- **依赖**: 与useImageStore有设备绑定关联
- **使用频率**: 🟡 中等

#### 10. useMockupShadowStore
- **位置**: `app/shared/hooks/useMockupShadowStore.ts`
- **职责**: 设备模拟阴影状态
- **状态内容**:
  - 阴影样式列表 (`shadowStyles`)
  - 当前阴影类型 (`shadowType`)
  - 透明度 (`opacity`)
  - 位置 (`position`)
- **依赖**: 与useMockupStore关联
- **使用频率**: 🟢 低 (视觉效果)

## 🔗 状态依赖关系分析

### 核心依赖链
```
useAppStore (核心)
├── useAppState (选择器)
├── useImageStore (业务核心)
│   ├── useCustomImageStore (功能扩展)
│   └── pasteUploadStore (功能扩展)
├── useSceneStore (视觉效果)
│   └── useBackgroundStore (背景)
│       └── useMagicBackgroundStore (特效)
└── useMockupStore (设备模拟)
    └── useMockupShadowStore (阴影)
```

### 隐式依赖关系
1. **useImageStore ↔ useAppStore**: 图片选择影响UI状态，模态框控制图片操作
2. **useSceneStore → useAppStore**: 场景切换需要更新UI状态
3. **useBackgroundStore → useAppStore**: 背景选择器的显示控制
4. **useMockupStore ↔ useImageStore**: 设备与图片的绑定关系

## 📈 整合优先级评估

### 🔴 高优先级 (立即整合)
1. **useAppStore + useAppState**: 核心应用状态，使用频率最高
2. **useImageStore + 相关扩展**: 核心业务逻辑，影响面最大

### 🟡 中优先级 (第二阶段)
3. **useSceneStore + useBackgroundStore**: 视觉效果相关，有明确关联
4. **useMockupStore + useMockupShadowStore**: 设备模拟相关，功能内聚

### 🟢 低优先级 (最后处理)
5. **useMagicBackgroundStore**: 特定功能，使用频率低
6. **useCustomImageStore**: 独立功能模块
7. **pasteUploadStore**: 辅助功能

## 🎯 阶段1完成标准

- [x] 完成所有状态源的详细分析
- [x] 绘制状态依赖关系图
- [x] 确定整合优先级排序
- [ ] 创建状态协调层设计方案
- [ ] 验证分析结果的准确性

## 📝 下一步行动

基于以上分析，阶段2将创建状态协调层，重点处理：
1. 核心应用状态的统一访问接口
2. 图片管理状态的协调机制
3. 跨状态源的同步解决方案
