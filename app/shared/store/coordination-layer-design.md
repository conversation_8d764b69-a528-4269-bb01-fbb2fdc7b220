# 🔗 状态协调层设计方案

## 🎯 设计目标

创建一个状态协调层，在不破坏现有9个状态源的前提下，提供：
1. **统一访问接口** - 组件通过协调层访问状态
2. **自动同步机制** - 处理跨状态源的数据同步
3. **渐进式迁移** - 支持逐步迁移现有组件

## 🏗️ 架构设计

### 协调层架构图
```
组件层 (Components)
    ↓ 统一接口
协调层 (Coordination Layer)
    ↓ 适配器模式
现有状态源 (Existing Stores)
```

### 核心组件

#### 1. 状态适配器 (State Adapters)
```typescript
// app/shared/store/adapters/core-app-adapter.ts
export class CoreAppAdapter {
    // 统一useAppStore和useAppState的访问
    getUIState() {
        return useAppStore.getState().ui
    }
    
    getModalState() {
        return useAppStore.getState().modal
    }
    
    setModal(key: string, isOpen: boolean) {
        useAppStore.getState().setModal(key, isOpen)
    }
}

// app/shared/store/adapters/image-adapter.ts
export class ImageAdapter {
    // 统一图片相关状态的访问
    getImages() {
        return useImageStore.getState().images
    }
    
    getSelectedImage() {
        const selectedId = useImageStore.getState().selectedImageId
        return this.getImages().find(img => img.id === selectedId)
    }
    
    selectImage(imageId: string) {
        useImageStore.getState().selectImage(imageId)
        // 自动同步相关状态
        this.syncImageSelection(imageId)
    }
    
    private syncImageSelection(imageId: string) {
        // 同步UI状态
        useAppStore.getState().setModal('mediaPickerModal', false)
        // 其他同步逻辑...
    }
}
```

#### 2. 状态同步器 (State Synchronizer)
```typescript
// app/shared/store/synchronizer.ts
export class StateSynchronizer {
    private subscriptions: Array<() => void> = []
    
    constructor() {
        this.setupSynchronization()
    }
    
    private setupSynchronization() {
        // 监听图片选择变化，同步UI状态
        this.subscriptions.push(
            useImageStore.subscribe((state, prevState) => {
                if (state.selectedImageId !== prevState.selectedImageId) {
                    this.onImageSelectionChange(state.selectedImageId)
                }
            })
        )
        
        // 监听场景变化，同步背景状态
        this.subscriptions.push(
            useSceneStore.subscribe((state, prevState) => {
                if (state.sceneType !== prevState.sceneType) {
                    this.onSceneTypeChange(state.sceneType)
                }
            })
        )
    }
    
    private onImageSelectionChange(imageId: string | null) {
        // 自动关闭相关模态框
        useAppStore.getState().setModal('mediaPickerModal', false)
        
        // 更新设备绑定显示
        if (imageId) {
            // 触发设备状态更新
            this.updateDeviceBinding(imageId)
        }
    }
    
    private onSceneTypeChange(sceneType: string) {
        // 根据场景类型自动调整背景
        if (sceneType === 'shadow') {
            useBackgroundStore.getState().setBackgroundType('transparent')
        }
    }
    
    destroy() {
        this.subscriptions.forEach(unsubscribe => unsubscribe())
    }
}
```

#### 3. 统一状态接口 (Unified State Interface)
```typescript
// app/shared/store/unified-interface.ts
export class UnifiedStateInterface {
    private coreAppAdapter = new CoreAppAdapter()
    private imageAdapter = new ImageAdapter()
    private synchronizer = new StateSynchronizer()
    
    // UI相关接口
    ui = {
        getIsMobile: () => this.coreAppAdapter.getUIState().isMobile,
        getModalState: () => this.coreAppAdapter.getModalState(),
        setModal: (key: string, isOpen: boolean) => 
            this.coreAppAdapter.setModal(key, isOpen),
        toggleModal: (key: string) => {
            const current = this.coreAppAdapter.getModalState()[key]
            this.coreAppAdapter.setModal(key, !current)
        }
    }
    
    // 图片相关接口
    images = {
        getAll: () => this.imageAdapter.getImages(),
        getSelected: () => this.imageAdapter.getSelectedImage(),
        select: (imageId: string) => this.imageAdapter.selectImage(imageId),
        upload: async (file: File) => {
            // 统一的上传逻辑，处理多个状态源的更新
            const result = await this.imageAdapter.uploadImage(file)
            // 自动同步相关状态
            return result
        }
    }
    
    // 视觉效果接口
    effects = {
        getScene: () => useSceneStore.getState(),
        setSceneType: (type: string) => {
            useSceneStore.getState().setSceneType(type)
            // 自动同步相关状态
        },
        getBackground: () => useBackgroundStore.getState(),
        setBackground: (type: string, value?: any) => {
            useBackgroundStore.getState().setBackgroundType(type)
            if (value) {
                useBackgroundStore.getState().setColor(value)
            }
        }
    }
    
    // 设备模拟接口
    devices = {
        getMockup: () => useMockupStore.getState(),
        getShadow: () => useMockupShadowStore.getState(),
        bindImageToDevice: (imageId: string, deviceIndex: number) => {
            // 统一处理设备绑定逻辑
            useImageStore.getState().bindImageToDevice(imageId, deviceIndex)
            // 同步相关状态
        }
    }
}

// 创建全局实例
export const unifiedState = new UnifiedStateInterface()
```

#### 4. React Hooks 适配器
```typescript
// app/shared/store/hooks.ts
import { unifiedState } from './unified-interface'
import { useEffect, useState } from 'react'

// 统一的UI状态hooks
export const useUnifiedUI = () => {
    const [isMobile, setIsMobile] = useState(unifiedState.ui.getIsMobile())
    const [modalState, setModalState] = useState(unifiedState.ui.getModalState())
    
    useEffect(() => {
        const unsubscribe = useAppStore.subscribe((state) => {
            setIsMobile(state.ui.isMobile)
            setModalState(state.modal)
        })
        return unsubscribe
    }, [])
    
    return {
        isMobile,
        modalState,
        setModal: unifiedState.ui.setModal,
        toggleModal: unifiedState.ui.toggleModal
    }
}

// 统一的图片状态hooks
export const useUnifiedImages = () => {
    const [images, setImages] = useState(unifiedState.images.getAll())
    const [selectedImage, setSelectedImage] = useState(unifiedState.images.getSelected())
    
    useEffect(() => {
        const unsubscribe = useImageStore.subscribe((state) => {
            setImages(state.images)
            setSelectedImage(state.images.find(img => img.id === state.selectedImageId))
        })
        return unsubscribe
    }, [])
    
    return {
        images,
        selectedImage,
        selectImage: unifiedState.images.select,
        uploadImage: unifiedState.images.upload
    }
}

// 统一的视觉效果hooks
export const useUnifiedEffects = () => {
    const [scene, setScene] = useState(unifiedState.effects.getScene())
    const [background, setBackground] = useState(unifiedState.effects.getBackground())
    
    useEffect(() => {
        const unsubscribeScene = useSceneStore.subscribe(setScene)
        const unsubscribeBackground = useBackgroundStore.subscribe(setBackground)
        
        return () => {
            unsubscribeScene()
            unsubscribeBackground()
        }
    }, [])
    
    return {
        scene,
        background,
        setSceneType: unifiedState.effects.setSceneType,
        setBackground: unifiedState.effects.setBackground
    }
}
```

## 🔄 渐进式迁移策略

### 阶段1：创建协调层基础设施
1. 实现状态适配器
2. 创建状态同步器
3. 建立统一接口

### 阶段2：迁移核心组件
1. 选择1-2个核心组件作为试点
2. 将组件迁移到使用统一接口
3. 验证功能完整性和性能

### 阶段3：全面推广
1. 逐步迁移所有组件
2. 优化协调层性能
3. 收集使用反馈

### 阶段4：逐步整合原始状态源
1. 基于协调层的使用经验
2. 逐个合并相关状态源
3. 最终移除协调层，形成统一架构

## ✅ 实施检查清单

- [ ] 创建状态适配器类
- [ ] 实现状态同步器
- [ ] 建立统一状态接口
- [ ] 创建React hooks适配器
- [ ] 选择试点组件进行迁移
- [ ] 验证功能完整性
- [ ] 性能测试和优化
- [ ] 文档和使用指南

这个协调层设计保持了现有状态源的完整性，同时提供了统一的访问接口和自动同步机制，为后续的状态源整合奠定了基础。
