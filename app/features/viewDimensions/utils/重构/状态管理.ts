// store.ts (或者你可以命名为 appState.ts, appStore.ts 等)

// 导入 Zustand 的核心创建函数
import { create } from 'zustand'
// 导入 devtools 中间件以支持 Redux DevTools 浏览器插件
import { devtools } from 'zustand/middleware'
// 导入预定义的视图尺寸数据，假设你的文件名为 'dimensions.ts'
// 请确保 './dimensions' 的路径相对于当前 'store.ts' 文件是正确的。
import { sizeInfo, viewSizes } from './dimensions' // 假设 ViewSize 也在 dimensions.ts 中导出
// 导入计算算法和获取功能
import { calculateDimensions } from './算法'
import { getAvailableSpace } from './获取'
// 导入统一的拖拽手柄配置
import { getDefaultViewControl, getViewControlFromLayout } from '../../../../config/dragHandleConfig'
// 导入布局相关的类型定义
import { LayoutType } from '../../../../components/DisplayContainer/DisplayConfig'

/**
 * 扩展Window接口以支持Redux DevTools
 */
declare global {
    interface Window {
        __REDUX_DEVTOOLS_EXTENSION__?: any
    }
}

/**
 * 注意：所有选择器函数已移至 app/hooks/useAppState.ts
 * 为了统一管理，请从 useAppState.ts 导入选择器函数，而不是直接从此文件导入
 */

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// 类型定义和常量 (这些是应用程序中数据的"蓝图"和固定值)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

/**
 * @enum DimensionSourceType
 * @description 标记当前画布尺寸是由程序默认提供，还是由用户自定义。
 */
export enum DimensionSourceType {
    /** 尺寸由应用程序的内置预设提供。 */
    Default = 'default',
    /** 尺寸由用户手动输入或修改。 */
    Custom = 'custom',
}

/**
 * @enum ImageExportFormat
 * @description 支持导出的图片文件格式。
 */
export enum ImageExportFormat {
    PNG = 'PNG',
    JPEG = 'JPEG',
}

/**
 * @enum ExportQualityKey
 * @description 用于程序内部快速引用不同的导出质量预设。
 * 例如，用 ExportQualityKey.Low 来获取低质量的导出配置。
 */
export enum ExportQualityKey {
    Low = 'Low', // 低质量
    Medium = 'Medium', // 中等质量
    High = 'High', // 高质量
}

/**
 * @enum ModalKey
 * @description 统一管理所有弹窗的键值，便于类型安全和代码维护。
 */
export enum ModalKey {
    /** 导出弹窗 */
    ExportPopover = 'exportPopover',
    /** 成功提示弹窗 */
    SuccessPopover = 'successPopover',
}

/**
 * @interface ExportQualitySetting
 * @description 定义了单个导出质量选项的具体配置。
 */
export interface ExportQualitySetting {
    /**
     * @property {string} scaleMultiplierLabel - 显示给用户的缩放倍数，比如 "1x", "2x"。
     */
    scaleMultiplierLabel: string
    /**
     * @property {string} resolutionTierName - 显示给用户的质量名称，比如 "FHD", "4K"。
     */
    resolutionTierName: string
    /**
     * @property {number} scaleFactor - 实际用于计算导出图片尺寸的缩放因子。
     * 例如，如果 scaleFactor 是 2，原图 100x100px 将导出为 200x200px。
     */
    scaleFactor: number
}

/**
 * @constant EXPORT_QUALITY_PRESETS
 * @description 一个包含所有预定义导出质量选项的配置对象。
 * 程序可以通过 ExportQualityKey 来方便地获取对应的 ExportQualitySetting。
 */
export const EXPORT_QUALITY_PRESETS: Record<ExportQualityKey, ExportQualitySetting> = {
    [ExportQualityKey.Low]: {
        scaleMultiplierLabel: '1x',
        resolutionTierName: 'FHD', // 全高清 (Full High Definition)
        scaleFactor: 1,
    },
    [ExportQualityKey.Medium]: {
        scaleMultiplierLabel: '2x',
        resolutionTierName: '4K', // 4K 超高清
        scaleFactor: 2,
    },
    [ExportQualityKey.High]: {
        scaleMultiplierLabel: '3x',
        resolutionTierName: '8K', // 8K 超高清
        scaleFactor: 3,
    },
}

/**
 * 移动设备断点宽度
 * 当屏幕宽度小于或等于此值时，认为是移动设备
 */
export const MOBILE_BREAKPOINT = 800

const initialDefaultViewDimensions: sizeInfo = viewSizes[0].sizes[2] // 默认视图尺寸1920x1440

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// Zustand Store 定义 (这里是应用程序状态管理的核心)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

/**
 * @interface CurrentViewDimensionsState
 * @description 定义当前视图/画布尺寸相关状态的结构。
 */
interface CurrentViewDimensionsState extends sizeInfo {
    sourceType: DimensionSourceType
    /** 计算后的实际显示宽度 */
    calculatedWidth?: number
    /** 计算后的实际显示高度 */
    calculatedHeight?: number
    /** 使用的计算方法描述 */
    calculationMethod?: string
}

/**
 * @interface CurrentExportSettingsState
 * @description 定义当前图像导出配置相关状态的结构。
 */
interface CurrentExportSettingsState {
    format: ImageExportFormat
    quality: ExportQualitySetting
}

/**
 * @interface UIState
 * @description 定义UI相关状态的结构。
 */
interface UIState {
    isMobile: boolean
}

/**
 * @interface DragHandlePosition
 * @description 拖拽手柄的位置信息
 * @property {number} x - 拖拽手柄在容器内的 X 坐标
 * @property {number} y - 拖拽手柄在容器内的 Y 坐标
 */
export interface DragHandlePosition {
    x: number
    y: number
}

/**
 * @interface ViewControlState
 * @description 定义视图控制相关状态的结构。
 */
interface ViewControlState {
    /** 视图缩放值，100 表示原始大小 */
    zoom: number
    /** 拖拽手柄位置，用于控制视图的焦点位置 */
    dragHandlePosition: DragHandlePosition
}

/**
 * @interface ModalState
 * @description 定义弹窗相关状态的结构。
 */
interface ModalState {
    /** 导出弹窗是否显示 */
    exportPopover: boolean
    /** 成功提示弹窗是否显示 */
    successPopover: boolean
}

/**
 * @interface ActiveLayoutState
 * @description 定义当前激活布局的状态结构
 * @property {LayoutType} type - 布局类型（单设备/双设备/三设备）
 * @property {number} id - 布局的唯一标识符
 */
export interface ActiveLayoutState {
    type: LayoutType
    id: number
}

/**
 * @interface ApplicationState
 * @description 定义了整个应用程序状态的结构。
 */
export interface ApplicationState {
    currentViewDimensions: CurrentViewDimensionsState
    currentExportSettings: CurrentExportSettingsState
    ui: UIState
    viewControl: ViewControlState
    modal: ModalState
    activeLayout: ActiveLayoutState

    setViewDimensions: (dimensionsUpdate: Partial<CurrentViewDimensionsState>) => void
    setExportFormat: (newFormat: ImageExportFormat) => void
    setExportQuality: (newQuality: ExportQualitySetting) => void
    setFullExportSettings: (settingsUpdate: Partial<CurrentExportSettingsState>) => void
    resetToDefaultDimensions: () => void
    setIsMobile: (isMobile: boolean) => void
    setZoom: (zoom: number) => void
    setDragHandlePosition: (position: DragHandlePosition) => void
    resetViewControl: () => void
    setModal: (key: ModalKey, isOpen: boolean) => void
    toggleModal: (key: ModalKey) => void
    setModalState: (modalUpdate: Partial<ModalState>) => void
    resetModalState: () => void

    /** 设置当前激活的布局 */
    setActiveLayout: (layout: ActiveLayoutState) => void
    /** 更新激活布局的部分属性 */
    updateActiveLayout: (layoutUpdate: Partial<ActiveLayoutState>) => void
    /** 重置激活布局到默认值 */
    resetActiveLayout: () => void

    /** 计算并更新视图尺寸 */
    calculateAndUpdateViewDimensions: () => void
    /** 设置视图尺寸并自动计算显示尺寸 */
    setViewDimensionsWithCalculation: (
        dimensionsUpdate: Partial<CurrentViewDimensionsState>,
    ) => void

    /**
     * @function isDefaultDimensionsAndMatches
     * @description 检查当前画布尺寸是否为默认来源，并且其宽度和高度与传入的参数匹配。
     * @param {string} key - 唯一值
     * @param {number} widthToCheck - 要检查的宽度。
     * @param {number} heightToCheck - 要检查的高度。
     * @returns {boolean} 如果是默认尺寸且宽高匹配则返回 true，否则返回 false。
     */
    isDefaultDimensionsAndMatches: (
        key: string,
        widthToCheck: number,
        heightToCheck: number,
    ) => boolean
}

/**
 * @constant useAppStore
 * @description 这是我们创建的 Zustand store，启用了 Redux DevTools 支持。
 */
export const useAppStore = create<ApplicationState>()(
    devtools(
        (set, get) => ({
            // <-- 注意这里添加了 get
            // ==============================
            // 1. 定义 Store 的初始状态 (Initial State)
            // ==============================
            currentViewDimensions: {
                key: initialDefaultViewDimensions.key, // 当前视图尺寸的唯一标识
                useWidth: initialDefaultViewDimensions.useWidth, // 当前视图宽度
                useHeight: initialDefaultViewDimensions.useHeight, // 当前视图高度
                ratioWidth: initialDefaultViewDimensions.ratioWidth, // 当前视图宽度比例
                ratioHeight: initialDefaultViewDimensions.ratioHeight, // 当前视图高度比例
                sourceType: DimensionSourceType.Default, // 当前视图尺寸来源
            },
            currentExportSettings: {
                format: ImageExportFormat.PNG,
                quality: EXPORT_QUALITY_PRESETS[ExportQualityKey.Low],
            },
            ui: {
                isMobile: false, // 默认为非移动设备
            },
            // 使用动态配置管理视图控制的初始状态，从默认的activeLayout配置中获取
            viewControl: getViewControlFromLayout(LayoutType.Single, 1),
            modal: {
                exportPopover: false, // 导出弹窗默认显示
                successPopover: false, // 成功提示弹窗默认隐藏
            },
            // activeLayout 布局状态初始值，默认为单设备布局的第一个配置
            activeLayout: {
                type: LayoutType.Single,
                id: 1,
            },

            // ==============================
            // 2. 定义 Store 的操作方法 (Actions)
            // ==============================
            setViewDimensions: (dimensionsUpdate: Partial<CurrentViewDimensionsState>) =>
                set((currentState: ApplicationState) => ({
                    currentViewDimensions: {
                        ...currentState.currentViewDimensions,
                        ...dimensionsUpdate,
                    },
                })),

            setExportFormat: (newFormat: ImageExportFormat) =>
                set((currentState: ApplicationState) => ({
                    currentExportSettings: {
                        ...currentState.currentExportSettings,
                        format: newFormat,
                    },
                })),

            setExportQuality: (newQuality: ExportQualitySetting) =>
                set((currentState: ApplicationState) => ({
                    currentExportSettings: {
                        ...currentState.currentExportSettings,
                        quality: newQuality,
                    },
                })),

            setFullExportSettings: (settingsUpdate: Partial<CurrentExportSettingsState>) =>
                set((currentState: ApplicationState) => ({
                    currentExportSettings: {
                        ...currentState.currentExportSettings,
                        ...settingsUpdate,
                    },
                })),

            resetToDefaultDimensions: () =>
                set(() => ({
                    currentViewDimensions: {
                        key: initialDefaultViewDimensions.key,
                        useWidth: initialDefaultViewDimensions.useWidth,
                        useHeight: initialDefaultViewDimensions.useHeight,
                        ratioWidth: initialDefaultViewDimensions.ratioWidth,
                        ratioHeight: initialDefaultViewDimensions.ratioHeight,
                        sourceType: DimensionSourceType.Default,
                    },
                })),

            setIsMobile: (isMobile: boolean) =>
                set((currentState: ApplicationState) => ({
                    ui: {
                        ...currentState.ui,
                        isMobile,
                    },
                })),

            /**
             * @function setZoom
             * @description 设置视图缩放值
             * @param {number} zoom - 缩放值，100表示原始大小
             */
            setZoom: (zoom: number) =>
                set((currentState: ApplicationState) => ({
                    viewControl: {
                        ...currentState.viewControl,
                        zoom,
                    },
                })),

            /**
             * @function setDragHandlePosition
             * @description 设置拖拽手柄位置
             * @param {DragHandlePosition} position - 拖拽手柄的新位置坐标
             * 例如：setDragHandlePosition({x: 100.5, y: 75.2})
             */
            setDragHandlePosition: (position: DragHandlePosition) =>
                set((currentState: ApplicationState) => ({
                    viewControl: {
                        ...currentState.viewControl,
                        dragHandlePosition: position,
                    },
                })),

            /**
             * @function resetViewControl
             * @description 重置视图控制状态到初始值
             * 使用统一配置管理默认值，避免硬编码参数分散导致的维护问题
             */
            resetViewControl: () =>
                set(() => ({
                    viewControl: getDefaultViewControl(),
                })),

            /**
             * @function setModal
             * @description 通过key设置指定弹窗的显示状态
             * @param {ModalKey} key - 弹窗的key（使用 ModalKey 枚举）
             * @param {boolean} isOpen - 是否显示弹窗
             */
            setModal: (key: ModalKey, isOpen: boolean) =>
                set((currentState: ApplicationState) => ({
                    modal: {
                        ...currentState.modal,
                        [key]: isOpen,
                    },
                })),

            /**
             * @function toggleModal
             * @description 切换指定弹窗的显示状态（显示变隐藏，隐藏变显示）
             * @param {ModalKey} key - 弹窗的key（使用 ModalKey 枚举）
             */
            toggleModal: (key: ModalKey) =>
                set((currentState: ApplicationState) => ({
                    modal: {
                        ...currentState.modal,
                        [key]: !currentState.modal[key],
                    },
                })),

            /**
             * @function setModalState
             * @description 设置弹窗状态（可以批量设置多个弹窗状态）
             * @param {Partial<ModalState>} modalUpdate - 要更新的弹窗状态
             */
            setModalState: (modalUpdate: Partial<ModalState>) =>
                set((currentState: ApplicationState) => ({
                    modal: {
                        ...currentState.modal,
                        ...modalUpdate,
                    },
                })),

            /**
             * @function resetModalState
             * @description 重置所有弹窗状态到初始值
             */
            resetModalState: () =>
                set(() => ({
                    modal: {
                        exportPopover: true,
                        successPopover: false,
                    },
                })),

            // ==============================
            // 5. activeLayout 布局状态管理方法
            // ==============================

            /**
             * @function setActiveLayout
             * @description 设置当前激活的布局，并同步更新viewControl
             * @param {ActiveLayoutState} layout - 要设置的布局状态，包含type和id
             * 例如：setActiveLayout({ type: LayoutType.Dual, id: 2 })
             */
            setActiveLayout: (layout: ActiveLayoutState) =>
                set(() => {
                    // 获取新布局对应的viewControl配置
                    const newViewControl = getViewControlFromLayout(layout.type, layout.id)
                    
                    return {
                        activeLayout: layout,
                        viewControl: newViewControl,
                    }
                }),

            /**
             * @function updateActiveLayout
             * @description 更新激活布局的部分属性，并同步更新viewControl
             * @param {Partial<ActiveLayoutState>} layoutUpdate - 要更新的布局属性
             * 例如：updateActiveLayout({ id: 3 }) 只更新id，保持type不变
             */
            updateActiveLayout: (layoutUpdate: Partial<ActiveLayoutState>) =>
                set((currentState: ApplicationState) => {
                    // 合并更新后的布局状态
                    const updatedLayout = {
                        ...currentState.activeLayout,
                        ...layoutUpdate,
                    }
                    
                    // 获取更新后布局对应的viewControl配置
                    const newViewControl = getViewControlFromLayout(updatedLayout.type, updatedLayout.id)
                    
                    return {
                        activeLayout: updatedLayout,
                        viewControl: newViewControl,
                    }
                }),

            /**
             * @function resetActiveLayout
             * @description 重置激活布局到默认值（单设备布局的第一个配置），并同步更新viewControl
             */
            resetActiveLayout: () =>
                set(() => {
                    // 默认布局配置
                    const defaultLayout = {
                        type: LayoutType.Single,
                        id: 1,
                    }
                    
                    // 获取默认布局对应的viewControl配置
                    const defaultViewControl = getViewControlFromLayout(defaultLayout.type, defaultLayout.id)
                    
                    return {
                        activeLayout: defaultLayout,
                        viewControl: defaultViewControl,
                    }
                }),

            // ==============================
            // 4. 新增：视图尺寸计算相关方法
            // ==============================

            /**
             * @function calculateAndUpdateViewDimensions
             * @description 计算并更新当前视图尺寸的实际显示尺寸和计算方法
             */
            calculateAndUpdateViewDimensions: () => {
                // 确保在客户端环境中运行
                if (typeof window === 'undefined') {
                    console.warn('SSR环境中跳过尺寸计算')
                    return
                }

                const { currentViewDimensions } = get()

                // 获取可用空间
                const availableSpace = getAvailableSpace()
                if (!availableSpace) {
                    console.warn('无法获取可用空间，跳过尺寸计算')
                    return
                }

                // 执行尺寸计算
                const calculationResult = calculateDimensions(availableSpace, {
                    useWidth: currentViewDimensions.useWidth,
                    useHeight: currentViewDimensions.useHeight,
                })

                // 更新状态
                set((state: ApplicationState) => ({
                    currentViewDimensions: {
                        ...state.currentViewDimensions,
                        calculatedWidth: calculationResult.width,
                        calculatedHeight: calculationResult.height,
                        calculationMethod: calculationResult.method,
                    },
                }))
            },

            /**
             * @function setViewDimensionsWithCalculation
             * @description 设置视图尺寸并自动计算显示尺寸
             * @param {Partial<CurrentViewDimensionsState>} dimensionsUpdate - 要更新的尺寸信息
             */
            setViewDimensionsWithCalculation: (
                dimensionsUpdate: Partial<CurrentViewDimensionsState>,
            ) => {
                // 首先更新基础尺寸信息
                set((currentState: ApplicationState) => ({
                    currentViewDimensions: {
                        ...currentState.currentViewDimensions,
                        ...dimensionsUpdate,
                    },
                }))

                // 然后触发计算
                get().calculateAndUpdateViewDimensions()
            },

            // ==============================
            // 3. 定义派生状态/辅助函数 (Derived State / Helper Functions)
            // ==============================
            /**
             * @function isDefaultDimensionsAndMatches (实现)
             * @description 检查当前画布尺寸是否为默认来源，并且其宽度和高度与传入的参数匹配。
             * @param {string} key - 唯一值
             * @param {number} widthToCheck - 要检查的宽度。
             * @param {number} heightToCheck - 要检查的高度。
             * @returns {boolean} 如果是默认尺寸且宽高匹配则返回 true，否则返回 false。
             */
            isDefaultDimensionsAndMatches: (
                key: string,
                widthToCheck: number,
                heightToCheck: number,
            ) => {
                const { currentViewDimensions } = get() // 使用 get() 获取当前状态
                return (
                    currentViewDimensions.key === key &&
                    currentViewDimensions.sourceType === DimensionSourceType.Default &&
                    currentViewDimensions.useWidth === widthToCheck &&
                    currentViewDimensions.useHeight === heightToCheck
                )
            },
        }),
        {
            // DevTools 配置选项
            name: 'AppStore', // 在 Redux DevTools 中显示的 store 名称
            // 强制启用，便于调试
            enabled: true,
        },
    ),
)

// 调试信息：在控制台输出 store 初始化状态
if (typeof window !== 'undefined') {
    console.log('🔧 Zustand Store 初始化完成')
    console.log('📊 Redux DevTools 支持:', window.__REDUX_DEVTOOLS_EXTENSION__ ? '✅' : '❌')
    console.log('🏪 当前 Store 状态:', useAppStore.getState())
}

/**
 * 初始化移动设备检测
 * 在客户端运行时调用此函数以设置和监听移动设备状态
 */
export const initMobileDetection = (): (() => void) => {
    // 确保在客户端环境中运行
    if (typeof window === 'undefined') return () => {}

    console.log('🔍 initMobileDetection 开始执行')
    console.log('🖥️ 窗口尺寸:', window.innerWidth, 'x', window.innerHeight)
    console.log('📱 移动设备断点:', MOBILE_BREAKPOINT, 'px')

    // 初始检测
    const updateMobileState = () => {
        const mediaQuery = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT}px)`)
        const isMobile = mediaQuery.matches
        console.log('📏 媒体查询结果:', mediaQuery.media, '→', isMobile)
        console.log('📱 设置 isMobile 为:', isMobile)

        useAppStore.getState().setIsMobile(isMobile)

        // 验证状态是否正确设置
        const currentState = useAppStore.getState().ui.isMobile
        console.log('✅ 状态设置后验证:', currentState)
    }

    // 立即执行一次检测
    updateMobileState()

    // 设置媒体查询监听器
    const mediaQuery = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT}px)`)
    const handleResize = (e: MediaQueryListEvent) => {
        console.log('📱 窗口尺寸改变:', e.matches ? '移动设备' : 'PC设备')
        useAppStore.getState().setIsMobile(e.matches)
    }

    mediaQuery.addEventListener('change', handleResize)

    // 返回清理函数
    return () => {
        console.log('🧹 清理移动设备检测监听器')
        mediaQuery.removeEventListener('change', handleResize)
    }
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// 如何在 React 组件中使用这个 Store (示例)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/*
// 步骤1: 在你的 React 组件文件中导入 store 和需要的类型/常量
import {
    useAppStore,
    DimensionSourceType,
    ImageExportFormat,
    ExportQualityKey,
    EXPORT_QUALITY_PRESETS,
    ApplicationState // 导入 ApplicationState 类型
} from './store';

function MyAwesomeComponent() {
    // 获取单个值：
    const currentWidth = useAppStore((state: ApplicationState) => state.currentViewDimensions.useWidth);

    // 获取整个对象：
    const viewDimensions = useAppStore((state: ApplicationState) => state.currentViewDimensions);
    const exportSettings = useAppStore((state: ApplicationState) => state.currentExportSettings);

    // 获取操作方法 (actions)
    const setViewDimensionsAction = useAppStore((state: ApplicationState) => state.setViewDimensions);
    const setExportFormatAction = useAppStore((state: ApplicationState) => state.setExportFormat);
    const setExportQualityAction = useAppStore((state: ApplicationState) => state.setExportQuality);
    const resetDimensionsAction = useAppStore((state: ApplicationState) => state.resetToDefaultDimensions);

    // 获取新的辅助函数
    const isDefaultAndMatches = useAppStore((state: ApplicationState) => state.isDefaultDimensionsAndMatches);

    // 事件处理函数
    const handleWidthInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newWidth = parseInt(event.target.value, 10);
        if (!isNaN(newWidth)) {
            setViewDimensionsAction({
                useWidth: newWidth,
                sourceType: DimensionSourceType.Custom
            });
        }
    };

    const switchToJpeg = () => {
        setExportFormatAction(ImageExportFormat.JPEG);
    };

    const setHighQuality = () => {
        setExportQualityAction(EXPORT_QUALITY_PRESETS[ExportQualityKey.High]);
    };

    const handleReset = () => {
        resetDimensionsAction();
    };

    // 使用新的函数
    const checkDimensions = (key: string, w: number, h: number) => {
        if (isDefaultAndMatches(key, w, h)) {
            console.log(`Current dimensions are default AND match ${w}x${h}`);
        } else {
            console.log(`Current dimensions are NOT default OR do not match ${w}x${h}`);
        }
    };


    return (
        <div>
            <h2>画布尺寸</h2>
            <p>
                宽度: {viewDimensions.useWidth}px,
                高度: {viewDimensions.useHeight}px
            </p>
            <p>来源: {viewDimensions.sourceType === DimensionSourceType.Default ? '默认' : '自定义'}</p>
            <label>
                修改宽度:
                <input type="number" value={currentWidth} onChange={handleWidthInputChange} />
            </label>
            <button onClick={handleReset}>重置为默认尺寸</button>

            <h2>导出设置</h2>
            <p>格式: {exportSettings.format.toUpperCase()}</p>
            <p>质量: {exportSettings.quality.resolutionTierName} ({exportSettings.quality.scaleMultiplierLabel})</p>
            <button onClick={switchToJpeg}>设为JPEG导出</button>
            <button onClick={setHighQuality}>设为8K高质量导出</button>

            <h2>检查尺寸</h2>
            <button onClick={() => checkDimensions(1920, 1080)}>检查是否为默认且1920x1080</button>
            <button onClick={() => checkDimensions(initialDefaultViewDimensions.useWidth, initialDefaultViewDimensions.useHeight)}>
                检查是否为默认且初始默认尺寸
            </button>
            <p>
                Is default and 1920x1080? {isDefaultAndMatches(initialDefaultViewDimensions.key, 1920, 1080) ? 'Yes' : 'No'}
            </p>
            <p>
                Is default and current initial default ({initialDefaultViewDimensions.useWidth}x{initialDefaultViewDimensions.useHeight})?
                {isDefaultAndMatches(initialDefaultViewDimensions.key, initialDefaultViewDimensions.useWidth, initialDefaultViewDimensions.useHeight) ? 'Yes' : 'No'}
            </p>
        </div>
    );
}

export default MyAwesomeComponent;
*/

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// 选择器函数 - 用于简化状态获取
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

/**
 * 获取UI是否处于移动设备模式
 * @returns 是否为移动设备模式
 */
export const useIsMobile = () => useAppStore(state => state.ui.isMobile)

/**
 * 获取当前视图尺寸
 * @returns 当前视图尺寸状态
 */
export const useViewDimensions = () => useAppStore(state => state.currentViewDimensions)

/**
 * 获取当前导出设置
 * @returns 当前导出设置状态
 */
export const useExportSettings = () => useAppStore(state => state.currentExportSettings)

/**
 * 获取当前弹窗状态
 * @returns 当前弹窗状态
 */
export const useModalState = () => useAppStore(state => state.modal)

/**
 * 获取当前视图控制状态
 * @returns 当前视图控制状态
 */
export const useViewControl = () => useAppStore(state => state.viewControl)

/**
 * 获取当前缩放值
 * @returns 当前缩放值
 */
export const useZoom = () => useAppStore(state => state.viewControl.zoom)

/**
 * 获取当前拖拽手柄位置
 * @returns 当前拖拽手柄位置
 */
export const useDragHandlePosition = () => useAppStore(state => state.viewControl.dragHandlePosition)

/**
 * 获取当前激活的布局状态
 * @returns 当前激活的布局状态，包含type和id
 */
export const useActiveLayout = () => useAppStore(state => state.activeLayout)

/**
 * 获取当前激活布局的类型
 * @returns 当前激活布局的类型（Single/Dual/Triple）
 */
export const useActiveLayoutType = () => useAppStore(state => state.activeLayout.type)

/**
 * 获取当前激活布局的ID
 * @returns 当前激活布局的ID
 */
export const useActiveLayoutId = () => useAppStore(state => state.activeLayout.id)
