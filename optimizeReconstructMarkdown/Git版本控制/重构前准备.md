# 🔧 Git版本控制：重构前准备

## 🎯 概述

在进行大规模重构前，必须建立完善的版本控制策略。采用**功能分支隔离策略**，为每个主要重构类别创建独立分支，确保任何时候都能安全回滚，避免不可挽回的损失。

## 🏗️ 分支策略设计

### 主分支结构

```
main (稳定主分支)
├── feature/01-文件组织和命名规范 ✅ (已完成)
├── feature/02-代码重复和冗余 ⏳ (待开始)
├── feature/03-状态管理复杂性 🔴 (高风险，当前执行)
├── feature/04-组件职责不清晰 ⏳ (待开始)
├── feature/05-性能优化空间 ⏳ (待开始)
├── feature/06-类型安全问题 ⏳ (待开始)
├── feature/07-错误处理不足 ⏳ (待开始)
└── feature/08-测试覆盖不足 ⏳ (待开始)
```

### 分支命名规范

- **主功能分支**: `feature/XX-功能描述`
- **子功能分支**: `feature/XX-功能描述/子功能`
- **修复分支**: `fix/XX-问题描述`
- **实验分支**: `experiment/XX-实验内容`

## 📋 重构前Git准备清单

### 1. 检查当前状态

```bash
# 检查当前分支状态
git status

# 检查是否有未提交的更改
git diff

# 检查是否有未跟踪的文件
git ls-files --others --exclude-standard
```

### 2. 清理工作区

```bash
# 如果有未提交的更改，先提交或暂存
git add .
git commit -m "重构前：保存当前工作进度"

# 或者使用 stash 暂存
git stash push -m "重构前暂存的更改"
```

### 3. 创建功能分支策略的完整备份

```bash
# 创建当前阶段的标签（重要！）
git tag -a v阶段完成-$(date '+%Y%m%d') -m "阶段完成备份 - $(date '+%Y-%m-%d %H:%M:%S')"

# 推送标签到远程（如果有远程仓库）
git push origin v阶段完成-$(date '+%Y%m%d')

# 为当前要执行的功能创建独立分支
# 例如：状态管理复杂性重构
git checkout -b feature/03-状态管理复杂性

# 再次确认当前状态
git log --oneline -5
git branch -a
```

### 4. 功能分支创建规范

```bash
# 创建新功能分支的标准流程
function 创建功能分支() {
    local 功能编号="$1"
    local 功能描述="$2"
    local 分支名="feature/${功能编号}-${功能描述}"

    echo "🔄 创建功能分支: $分支名"

    # 确保在最新的main分支上
    git checkout main
    git pull origin main

    # 创建并切换到新分支
    git checkout -b "$分支名"

    # 创建分支创建标签
    git tag -a "分支创建-${功能编号}" -m "创建${功能描述}功能分支 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 推送新分支到远程
    git push -u origin "$分支名"
    git push origin "分支创建-${功能编号}"

    echo "✅ 功能分支 $分支名 创建完成"
}

# 使用示例
创建功能分支 "03" "状态管理复杂性"
创建功能分支 "04" "组件职责不清晰"
```

## 🔄 功能分支中的Git策略

### 当前分支状态 ✅

- **重构集成分支**: `feature/refactor-integration` 已创建
- **当前工作分支**: `feature/03-状态管理复杂性`
- **分支基础**: 基于 `feature/refactor-integration`

### 功能内分阶段提交策略

```bash
# 在功能分支内按子步骤提交
# 例如：在 feature/03-状态管理复杂性 分支中

# 子阶段1：状态分析和设计
git add optimizeReconstructMarkdown/ app/shared/store/
git commit -m "feat(state): 完成状态管理现状分析和策略设计

- 分析识别9个分散状态源
- 设计渐进式三阶段重构策略
- 创建状态依赖关系图
- 规划整合优先级"

# 子阶段2：创建基础Store结构
git add app/shared/store/index.ts app/shared/store/slices/
git commit -m "feat(state): 实现统一Store基础结构

- 创建主Store入口文件
- 实现UI和Canvas slice
- 添加选择器hooks"

# 子阶段3：迁移现有状态
git add app/components/ app/features/
git commit -m "refactor(state): 迁移组件到统一状态管理

- 更新组件状态引用
- 保持向后兼容性
- 验证功能完整性"

# 子阶段4：清理旧状态管理
git rm app/old-store-files/
git commit -m "cleanup(state): 清理旧状态管理文件

- 移除废弃的store文件
- 更新导入路径
- 完成状态管理重构"
```

### 功能完成后的合并策略

```bash
# 功能开发完成后的标准流程
function 完成功能开发() {
    local 功能分支="$1"
    local 功能描述="$2"

    echo "🔄 完成功能开发: $功能分支"

    # 1. 在功能分支上创建完成标签
    git tag -a "完成-${功能分支##*/}" -m "${功能描述}功能开发完成 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 2. 推送功能分支和标签
    git push origin "$功能分支"
    git push origin "完成-${功能分支##*/}"

    # 3. 切换到main分支并更新
    git checkout main
    git pull origin main

    # 4. 合并功能分支（使用--no-ff保持分支历史）
    git merge --no-ff "$功能分支" -m "feat: 完成${功能描述}

合并功能分支: $功能分支
完成时间: $(date '+%Y-%m-%d %H:%M:%S')"

    # 5. 推送合并结果
    git push origin main

    # 6. 创建里程碑标签
    git tag -a "milestone-${功能分支##*/}" -m "里程碑: ${功能描述}完成"
    git push origin "milestone-${功能分支##*/}"

    echo "✅ 功能 $功能描述 开发完成并合并到main"
}

# 使用示例
完成功能开发 "feature/03-状态管理复杂性" "状态管理复杂性重构"
```

### 每阶段验证点

```bash
# 在每个阶段完成后执行验证
function 验证当前阶段() {
    echo "🔍 开始验证当前阶段..."

    # TypeScript 编译检查
    if npx tsc --noEmit; then
        echo "✅ TypeScript 编译通过"
    else
        echo "❌ TypeScript 编译失败，需要修复"
        return 1
    fi

    # 尝试启动开发服务器
    timeout 30s npm run dev > /dev/null 2>&1
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then
        echo "✅ 开发服务器启动正常"
    else
        echo "❌ 开发服务器启动失败"
        return 1
    fi

    echo "✅ 当前阶段验证通过，可以继续下一阶段"
    return 0
}

# 在每次提交前调用验证函数
验证当前阶段 && git commit -m "阶段X：验证通过的提交"
```

## 🚨 回滚策略

### 1. 单阶段回滚

```bash
# 回滚到上一个提交（撤销最后一次更改）
git reset --hard HEAD~1

# 查看回滚后的状态
git status
git log --oneline -3
```

### 2. 多阶段回滚

```bash
# 查看提交历史，找到要回滚到的提交
git log --oneline -10

# 回滚到指定提交（例如回滚3个提交）
git reset --hard HEAD~3

# 或者回滚到特定的提交哈希
git reset --hard <commit-hash>
```

### 3. 完整回滚到重构前

```bash
# 回滚到重构前的标签
git reset --hard v重构前稳定版

# 清理所有未跟踪的文件
git clean -fd

# 确认回滚成功
git status
git log --oneline -3
```

### 4. 紧急回滚（如果重构分支出现严重问题）

```bash
# 切换回主分支
git checkout main

# 删除有问题的重构分支
git branch -D feature/文件结构重构

# 从重构前标签重新创建分支
git checkout -b feature/文件结构重构-修复版 v重构前稳定版
```

## 🔍 问题诊断和修复

### 常见问题和解决方案

#### 1. 导入路径错误

```bash
# 查找所有包含旧导入路径的文件
grep -r "from './MobileMockup_Layout'" app/

# 批量替换导入路径
find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i "s|from './MobileMockup_Layout'|from '@/components/layout/mobileMockupLayout'|g"
```

#### 2. TypeScript 编译错误

```bash
# 详细查看编译错误
npx tsc --noEmit --pretty

# 检查 tsconfig.json 路径别名配置
cat tsconfig.json | grep -A 10 "paths"
```

#### 3. 文件移动后找不到模块

```bash
# 检查文件是否真的移动到了正确位置
find app/ -name "*mobileMockupLayout*"

# 检查文件权限
ls -la app/components/layout/mobileMockupLayout.tsx
```

## 📊 重构进度跟踪

### 创建进度跟踪文件

```bash
# 创建进度跟踪文件
cat > 重构进度.md << 'EOF'
# 文件结构重构进度跟踪

## 总体进度
- [ ] 第一阶段：根目录清理
- [ ] 第二阶段：样式文件移动
- [ ] 第三阶段：共享资源移动
- [ ] 第四阶段：布局组件移动
- [ ] 第五阶段：业务组件移动
- [ ] 第六阶段：模态框组件移动
- [ ] 第七阶段：功能模块移动
- [ ] 第八阶段：主要文件更新

## 详细记录
### $(date '+%Y-%m-%d %H:%M:%S') - 开始重构
- 创建了重构前标签：v重构前稳定版
- 创建了重构分支：feature/文件结构重构

EOF

# 将进度文件加入版本控制
git add 重构进度.md
git commit -m "添加重构进度跟踪文件"
```

### 更新进度的脚本

```bash
# 创建更新进度的函数
function 更新重构进度() {
    local 阶段名称="$1"
    local 状态="$2"  # "开始" 或 "完成"

    echo "### $(date '+%Y-%m-%d %H:%M:%S') - ${阶段名称}${状态}" >> 重构进度.md

    if [ "$状态" = "完成" ]; then
        echo "- ✅ ${阶段名称}已完成" >> 重构进度.md
        echo "- 提交哈希: $(git rev-parse HEAD)" >> 重构进度.md
    else
        echo "- 🔄 开始${阶段名称}" >> 重构进度.md
    fi

    echo "" >> 重构进度.md

    # 提交进度更新
    git add 重构进度.md
    git commit -m "更新重构进度：${阶段名称}${状态}"
}

# 使用示例
更新重构进度 "第一阶段根目录清理" "开始"
# ... 执行第一阶段工作 ...
更新重构进度 "第一阶段根目录清理" "完成"
```

## ✅ 重构完成后的验证

### 最终验证清单

```bash
# 1. 完整的功能测试
npm run dev
# 手动测试所有主要功能

# 2. 构建测试
npm run build

# 3. 类型检查
npx tsc --noEmit

# 4. 代码规范检查
npx eslint app/ --ext .ts,.tsx

# 5. 测试运行（如果有测试）
npm test

# 6. 性能检查
npm run build && npm run start
# 检查页面加载速度和功能响应
```

### 创建重构完成标签

```bash
# 重构完成后创建新标签
git tag -a v重构完成 -m "文件结构重构完成 - $(date '+%Y-%m-%d %H:%M:%S')"

# 推送到远程
git push origin feature/文件结构重构
git push origin v重构完成

# 合并到主分支（在确认一切正常后）
git checkout main
git merge feature/文件结构重构
git push origin main
```

这个Git版本控制策略确保了重构过程的每一步都是安全可控的，你觉得这样的安排如何？
