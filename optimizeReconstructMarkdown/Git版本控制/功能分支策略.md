# 🌿 Git功能分支策略

## 🎯 策略概述

采用**功能分支隔离策略**，为每个主要重构类别创建独立的feature分支，确保不同功能的开发互不干扰，支持独立测试、回滚和合并。

## 🏗️ 分支架构设计

### 主分支结构

```
main (生产稳定主分支)
└── feature/refactor-integration (重构集成分支)
    ├── feature/01-文件组织和命名规范 ✅ (已完成)
    ├── feature/02-代码重复和冗余 ⏳ (待开始)
    ├── feature/03-状态管理复杂性 🔴 (高风险，当前执行)
    ├── feature/04-组件职责不清晰 ⏳ (待开始)
    ├── feature/05-性能优化空间 ⏳ (待开始)
    ├── feature/06-类型安全问题 ⏳ (待开始)
    ├── feature/07-错误处理不足 ⏳ (待开始)
    └── feature/08-测试覆盖不足 ⏳ (待开始)
```

### 分支层级说明

- **main**: 生产环境稳定分支，只接受经过完整测试的重构成果
- **feature/refactor-integration**: 重构集成分支，所有功能分支的集成和测试环境
- **feature/XX-功能描述**: 具体功能的开发分支，从feature/refactor-integration分出，完成后合并回feature/refactor-integration

### 分支生命周期

```
创建功能分支 → 功能开发 → 测试验证 → 合并到feature/refactor-integration → 集成测试 → 合并到main → 删除分支
     ↓           ↓         ↓           ↓                            ↓          ↓         ↓
   标签备份     阶段提交   功能测试    功能完成标签                  集成验证   里程碑    清理
```

### 工作流程详解

1. **功能分支开发**: 从feature/refactor-integration创建具体功能分支
2. **功能完成**: 合并回feature/refactor-integration，进行集成测试
3. **阶段性发布**: 将feature/refactor-integration的稳定版本合并到main
4. **生产发布**: 从main分支进行生产部署

## 🚀 分支操作指南

### 1. 创建新功能分支

```bash
# 1. 首先创建feature/refactor-integration分支（如果不存在）
function 创建重构集成分支() {
    echo "🔄 创建重构集成分支: feature/refactor-integration"

    # 确保在最新的main分支上
    git checkout main
    git pull origin main

    # 创建重构开始备份标签
    git tag -a "重构开始备份" -m "开始重构前的稳定版本 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 创建并切换到feature/refactor-integration分支
    git checkout -b feature/refactor-integration

    # 推送新分支到远程
    git push -u origin feature/refactor-integration
    git push origin "重构开始备份"

    echo "✅ 重构集成分支 feature/refactor-integration 创建完成"
}

# 2. 从feature/refactor-integration创建具体功能分支
function 创建功能分支() {
    local 功能编号="$1"
    local 功能描述="$2"
    local 分支名="feature/${功能编号}-${功能描述}"

    echo "🔄 创建功能分支: $分支名"

    # 确保在最新的feature/refactor-integration分支上
    git checkout feature/refactor-integration
    git pull origin feature/refactor-integration

    # 创建当前状态备份标签
    git tag -a "备份-开始${功能编号}" -m "开始${功能描述}前的备份 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 创建并切换到新分支
    git checkout -b "$分支名"

    # 推送新分支到远程
    git push -u origin "$分支名"
    git push origin "备份-开始${功能编号}"

    echo "✅ 功能分支 $分支名 创建完成"
    echo "📋 当前分支: $(git branch --show-current)"
}

# 使用示例
创建重构集成分支
创建功能分支 "03" "状态管理复杂性"
```

### 2. 功能分支内的开发流程

```bash
# 在功能分支内按子步骤开发
# 例如：在 feature/03-状态管理复杂性 分支中

# 子步骤1：分析和设计
git add docs/ design/
git commit -m "feat(state): 分析当前状态管理架构

- 识别6个分散的状态管理源
- 设计统一状态管理架构
- 制定迁移策略"

# 子步骤2：创建基础结构
git add app/shared/store/types.ts app/shared/store/index.ts
git commit -m "feat(state): 创建统一Store基础结构

- 定义统一状态类型
- 实现Store主入口
- 配置DevTools支持"

# 子步骤3：实现功能Slice
git add app/shared/store/slices/
git commit -m "feat(state): 实现UI和Canvas状态slice

- 创建UI状态管理slice
- 创建Canvas状态管理slice
- 添加选择器hooks"

# 子步骤4：迁移现有组件
git add app/components/ app/features/
git commit -m "refactor(state): 迁移组件到统一状态管理

- 更新组件状态引用
- 保持向后兼容性
- 验证功能完整性"

# 子步骤5：清理和优化
git rm app/old-stores/
git commit -m "cleanup(state): 清理旧状态管理文件

- 移除废弃的store文件
- 更新所有导入路径
- 完成状态管理重构"
```

### 3. 功能完成后的合并流程

```bash
# 功能开发完成后的标准合并流程
function 完成功能开发() {
    local 功能分支="$1"
    local 功能描述="$2"

    echo "🔄 完成功能开发: $功能分支"

    # 1. 最终测试验证
    echo "🧪 执行最终测试..."
    if ! npm run build || ! npm run test; then
        echo "❌ 测试失败，请修复后再合并"
        return 1
    fi

    # 2. 在功能分支上创建完成标签
    git tag -a "完成-${功能分支##*/}" -m "${功能描述}功能开发完成 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 3. 推送功能分支和标签
    git push origin "$功能分支"
    git push origin "完成-${功能分支##*/}"

    # 4. 切换到feature/main分支并更新
    git checkout feature/main
    git pull origin feature/main

    # 5. 合并功能分支到feature/main（使用--no-ff保持分支历史）
    git merge --no-ff "$功能分支" -m "feat: 完成${功能描述}

合并功能分支: $功能分支
完成时间: $(date '+%Y-%m-%d %H:%M:%S')

主要变更:
- 实现了${功能描述}相关功能
- 通过了所有测试验证
- 保持了向后兼容性"

    # 6. 推送合并结果到feature/main
    git push origin feature/main

    # 7. 创建功能完成标签
    git tag -a "集成-${功能分支##*/}" -m "功能集成: ${功能描述}已集成到feature/main"
    git push origin "集成-${功能分支##*/}"

    # 8. 清理功能分支（可选）
    read -p "是否删除功能分支 $功能分支? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git branch -d "$功能分支"
        git push origin --delete "$功能分支"
        echo "🗑️ 已删除功能分支 $功能分支"
    fi

    echo "✅ 功能 $功能描述 开发完成并集成到feature/main"
}

# 4. 阶段性发布到main分支
function 发布到主分支() {
    local 发布描述="$1"

    echo "🚀 准备发布到main分支: $发布描述"

    # 1. 确保feature/main是最新的
    git checkout feature/main
    git pull origin feature/main

    # 2. 执行完整测试
    echo "🧪 执行完整测试套件..."
    if ! npm run build || ! npm run test || ! npm run e2e; then
        echo "❌ 测试失败，无法发布到main"
        return 1
    fi

    # 3. 切换到main分支
    git checkout main
    git pull origin main

    # 4. 合并feature/main到main
    git merge --no-ff feature/main -m "release: ${发布描述}

发布时间: $(date '+%Y-%m-%d %H:%M:%S')
包含功能: 详见feature/main分支历史
测试状态: 全部通过"

    # 5. 推送到main并创建里程碑
    git push origin main
    git tag -a "milestone-$(date '+%Y%m%d')" -m "里程碑: ${发布描述}"
    git push origin "milestone-$(date '+%Y%m%d')"

    echo "✅ 成功发布到main分支"
}

# 使用示例
完成功能开发 "feature/03-状态管理复杂性" "状态管理复杂性重构"
发布到主分支 "完成状态管理重构阶段"
```

## 🔄 分支切换和状态管理

### 在不同功能分支间切换

```bash
# 安全切换到其他功能分支
function 切换功能分支() {
    local 目标分支="$1"

    echo "🔄 切换到功能分支: $目标分支"

    # 1. 检查当前工作区状态
    if [ -n "$(git status --porcelain)" ]; then
        echo "⚠️ 当前有未提交的更改"
        read -p "是否暂存当前更改? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git stash push -m "切换分支前的暂存 - $(date '+%Y-%m-%d %H:%M:%S')"
        else
            echo "❌ 请先处理未提交的更改"
            return 1
        fi
    fi

    # 2. 切换分支
    git checkout "$目标分支"
    git pull origin "$目标分支"

    # 3. 显示当前状态
    echo "✅ 已切换到分支: $(git branch --show-current)"
    echo "📋 最近提交:"
    git log --oneline -3
}

# 使用示例
切换功能分支 "feature/04-组件职责不清晰"
```

### 功能分支状态同步

```bash
# 将main分支的最新更改同步到功能分支
function 同步主分支更改() {
    local 当前分支=$(git branch --show-current)

    if [[ $当前分支 != feature/* ]]; then
        echo "❌ 当前不在功能分支上"
        return 1
    fi

    echo "🔄 同步main分支更改到 $当前分支"

    # 1. 暂存当前更改
    git stash push -m "同步前暂存 - $(date '+%Y-%m-%d %H:%M:%S')"

    # 2. 获取main分支最新更改
    git fetch origin main

    # 3. 合并main分支更改
    git merge origin/main -m "sync: 同步main分支最新更改到$当前分支"

    # 4. 恢复暂存的更改
    if git stash list | grep -q "同步前暂存"; then
        git stash pop
    fi

    echo "✅ 同步完成"
}
```

## 📊 分支状态监控

### 查看所有功能分支状态

```bash
# 显示所有功能分支的状态
function 查看功能分支状态() {
    echo "📊 功能分支状态概览"
    echo "===================="

    # 获取所有功能分支
    local 功能分支列表=$(git branch -r | grep "origin/feature/" | sed 's/origin\///')

    for 分支 in $功能分支列表; do
        echo "🌿 $分支"

        # 获取分支最后提交信息
        local 最后提交=$(git log --oneline -1 origin/$分支 2>/dev/null)
        echo "   📝 最后提交: $最后提交"

        # 检查是否有未合并的更改
        local 未合并数=$(git rev-list --count origin/main..origin/$分支 2>/dev/null || echo "0")
        echo "   📊 未合并提交: $未合并数"

        # 检查分支年龄
        local 创建时间=$(git log --format="%cr" origin/$分支 | tail -1 2>/dev/null)
        echo "   ⏰ 创建时间: $创建时间"

        echo ""
    done
}
```

## 🚨 紧急情况处理

### 功能分支紧急回滚

```bash
# 功能分支出现问题时的紧急回滚
function 紧急回滚功能分支() {
    local 功能分支="$1"
    local 回滚目标="$2"  # 标签名或提交哈希

    echo "🚨 紧急回滚功能分支: $功能分支"

    # 1. 切换到功能分支
    git checkout "$功能分支"

    # 2. 创建回滚前备份
    git tag -a "回滚前备份-$(date '+%Y%m%d-%H%M%S')" -m "紧急回滚前的备份"

    # 3. 执行回滚
    git reset --hard "$回滚目标"

    # 4. 强制推送（谨慎操作）
    read -p "确认强制推送回滚结果? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git push --force-with-lease origin "$功能分支"
        echo "✅ 紧急回滚完成"
    else
        echo "❌ 回滚已取消"
    fi
}
```

## ✅ 最佳实践总结

### 功能分支开发原则

1. **单一职责**: 每个功能分支只处理一个主要功能
2. **小步快跑**: 频繁提交，保持每个提交都是可工作的状态
3. **充分测试**: 每个子步骤都要经过测试验证
4. **清晰命名**: 使用描述性的分支名和提交信息
5. **及时同步**: 定期同步main分支的更改

### 分支管理规范

1. **创建前备份**: 每次创建新分支前都要创建备份标签
2. **阶段性标签**: 重要节点创建标签便于回滚
3. **合并策略**: 使用--no-ff保持分支历史
4. **清理机制**: 合并后及时清理不需要的分支

这个功能分支策略确保了项目重构的安全性和可控性，你觉得这样的设计如何？
