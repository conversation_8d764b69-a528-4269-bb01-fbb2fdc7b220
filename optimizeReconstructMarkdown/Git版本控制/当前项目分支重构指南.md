# 🔄 当前项目分支重构指南

## 🎯 目标

将当前项目的分支结构调整为简化的Git策略：

```
main (生产稳定主分支 - 保持不变)
└── feature/refactor-integration (唯一重构工作分支)
    ├── 01-文件组织和命名规范 ✅ (已完成)
    ├── 03-状态管理复杂性 🔴 (当前执行)
    └── ... (其他重构阶段)
```

## 📊 当前分支状态分析

### 现有分支

- ✅ `main` - 主分支，**保持不变，不进行任何操作**
- ❌ `feature/文件结构重构` - 需要合并到 `feature/refactor-integration`
- ✅ `feature/03-状态管理复杂性` - 需要合并到 `feature/refactor-integration`
- ❌ `feature/refactor-integration` - 需要创建作为唯一工作分支
- ⚪ `release-v1.0.0` - 旧版本分支，保留不动
- ⚪ `story-1_完全使用shots样式-release-v1.1.0` - 旧分支，保留不动

## 🚀 重构实施步骤

### 步骤1：创建唯一重构工作分支

```bash
# 1. 从main分支创建feature/refactor-integration分支
git checkout main
git pull origin main

# 2. 创建重构开始备份标签
git tag -a "重构开始备份" -m "开始重构前的稳定版本 - $(date '+%Y-%m-%d %H:%M:%S')"

# 3. 创建并切换到feature/refactor-integration分支
git checkout -b feature/refactor-integration

# 4. 推送新分支到远程
git push -u origin feature/refactor-integration
git push origin "重构开始备份"

echo "✅ 重构工作分支 feature/refactor-integration 创建完成"
```

### 步骤2：合并现有功能分支内容

```bash
# 1. 合并feature/文件结构重构的内容
git checkout feature/refactor-integration
git merge --no-ff feature/文件结构重构 -m "feat: 合并文件组织和命名规范重构

- 重构项目文件结构
- 统一命名规范
- 清理根目录文件
- 阶段1完成"

# 2. 创建阶段完成标签
git tag -a "阶段1-文件组织完成" -m "文件组织和命名规范重构完成 - $(date '+%Y-%m-%d %H:%M:%S')"

# 3. 合并feature/03-状态管理复杂性的内容
git merge --no-ff feature/03-状态管理复杂性 -m "feat: 合并状态管理复杂性重构进度

- 状态管理分析和设计
- 执行控制器配置
- 阶段3进行中"

# 4. 推送合并结果
git push origin feature/refactor-integration
git push origin --tags

echo "✅ 现有功能分支内容合并完成"
```

### 步骤3：清理旧分支

```bash
# 1. 删除已合并的功能分支（本地）
git branch -d feature/文件结构重构
git branch -d feature/03-状态管理复杂性

# 2. 删除远程分支（可选，建议保留一段时间）
# git push origin --delete feature/文件结构重构
# git push origin --delete feature/03-状态管理复杂性

echo "✅ 旧分支清理完成"
```

### 步骤4：切换到统一工作分支

```bash
# 1. 确保在feature/refactor-integration分支上
git checkout feature/refactor-integration
git pull origin feature/refactor-integration

# 2. 验证当前状态
git status
git log --oneline -5

echo "✅ 已切换到统一重构工作分支，可以继续重构工作"
```

## 🔍 验证重构结果

### 检查分支结构

```bash
# 查看所有分支
git branch -a

# 预期结果应该包含：
# * feature/03-状态管理复杂性
#   feature/refactor-integration
#   main
#   remotes/origin/feature/03-状态管理复杂性
#   remotes/origin/feature/refactor-integration
#   remotes/origin/main
```

### 检查分支关系

```bash
# 查看分支图
git log --oneline --graph --all -10

# 验证feature/03-状态管理复杂性基于feature/refactor-integration
git merge-base feature/refactor-integration feature/03-状态管理复杂性
```

### 检查标签

```bash
# 查看所有标签
git tag --list

# 预期应该包含：
# - 重构分支结构前备份
# - 集成-01-文件组织
# - 其他历史标签
```

## ⚠️ 注意事项

### 风险控制

1. **备份标签**: 每个重要操作前都创建备份标签
2. **强制推送**: 使用 `--force-with-lease` 而不是 `--force`
3. **分步执行**: 一步一步执行，每步验证结果
4. **保留历史**: 旧分支可以保留一段时间，确认无问题后再删除

### 回滚方案

如果重构过程中出现问题，可以使用备份标签回滚：

```bash
# 回滚到重构前状态
git checkout main
git reset --hard "重构分支结构前备份"
git push --force-with-lease origin main
```

### 团队协作

如果有其他开发者，需要：

1. 通知团队分支结构变更
2. 提供新的分支使用指南
3. 协助团队成员更新本地分支

## ✅ 完成检查清单

- [ ] 创建feature/refactor-integration分支
- [ ] 合并feature/文件结构重构内容到统一分支
- [ ] 合并feature/03-状态管理复杂性内容到统一分支
- [ ] 清理旧功能分支
- [ ] 切换到统一工作分支
- [ ] 验证分支结构正确性
- [ ] 创建必要的备份标签
- [ ] 更新相关文档

完成这个重构后，项目将拥有清晰的分支结构，支持后续的功能开发和集成。
