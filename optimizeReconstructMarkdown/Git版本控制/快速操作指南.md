# ⚡ Git快速操作指南

## 🎯 概述

这是项目重构过程中Git操作的快速参考指南，提供常用命令和最佳实践。

## 🚀 当前项目状态

### 分支结构

```
main (稳定主分支)
└── feature/refactor-integration (重构集成分支) ✅ 已创建
    ├── feature/文件结构重构 ❌ (需要重命名为 feature/01-文件组织和命名规范)
    ├── feature/03-状态管理复杂性 ✅ (当前工作分支)
    ├── feature/04-组件职责不清晰 ⏳ (待创建)
    └── ... (其他功能分支)
```

### 当前状态

- ✅ **重构集成分支已创建**: `feature/refactor-integration`
- ✅ **状态管理分支已创建**: `feature/03-状态管理复杂性`
- ❌ **需要重命名**: `feature/文件结构重构` → `feature/01-文件组织和命名规范`
- ⏳ **待调整**: 分支基础关系需要基于 `feature/refactor-integration`

## 🔄 立即需要执行的Git操作

### 1. 处理当前状态

```bash
# 检查当前状态
git status
git branch --show-current

# 提交当前状态管理重构的初始工作
git add app/shared/store/ optimizeReconstructMarkdown/
git commit -m "feat(state): 初始化统一状态管理架构

- 创建统一状态类型定义
- 实现UI和Canvas slice基础结构
- 设计状态管理架构文档
- 更新Git版本控制策略"
```

### 2. 创建状态管理专用分支

```bash
# 从当前分支创建状态管理分支
git checkout -b feature/03-状态管理复杂性

# 推送新分支到远程
git push -u origin feature/03-状态管理复杂性

# 创建分支创建标签
git tag -a "分支创建-状态管理" -m "创建状态管理复杂性功能分支 - $(date '+%Y-%m-%d %H:%M:%S')"
git push origin "分支创建-状态管理"
```

### 3. 更新分支状态

```bash
# 切换回文件结构重构分支处理遗留工作
git checkout feature/文件结构重构

# 如果文件结构重构已完成，合并到main
git checkout main
git pull origin main
git merge --no-ff feature/文件结构重构 -m "feat: 完成文件结构重构

- 重构项目文件结构
- 统一命名规范
- 清理根目录文件"

# 推送合并结果
git push origin main

# 创建里程碑标签
git tag -a "milestone-文件结构重构" -m "里程碑: 文件结构重构完成"
git push origin "milestone-文件结构重构"
```

## 📋 常用Git命令速查

### 分支操作

```bash
# 查看所有分支
git branch -a

# 创建并切换到新分支
git checkout -b feature/新功能名称

# 切换分支
git checkout 分支名

# 删除本地分支
git branch -d 分支名

# 删除远程分支
git push origin --delete 分支名
```

### 提交操作

```bash
# 查看状态
git status

# 添加文件
git add .                    # 添加所有文件
git add 文件路径              # 添加特定文件

# 提交
git commit -m "类型: 简短描述"

# 修改最后一次提交
git commit --amend -m "新的提交信息"
```

### 标签操作

```bash
# 创建标签
git tag -a 标签名 -m "标签描述"

# 推送标签
git push origin 标签名

# 查看所有标签
git tag --list

# 删除标签
git tag -d 标签名
git push origin --delete 标签名
```

### 同步操作

```bash
# 获取远程更新
git fetch origin

# 拉取并合并
git pull origin 分支名

# 推送到远程
git push origin 分支名

# 推送所有标签
git push origin --tags
```

## 🚨 紧急操作

### 回滚操作

```bash
# 回滚到上一个提交
git reset --hard HEAD~1

# 回滚到特定提交
git reset --hard 提交哈希

# 回滚到特定标签
git reset --hard 标签名

# 清理未跟踪的文件
git clean -fd
```

### 暂存操作

```bash
# 暂存当前更改
git stash push -m "暂存描述"

# 查看暂存列表
git stash list

# 恢复最新暂存
git stash pop

# 恢复特定暂存
git stash apply stash@{0}
```

## 🔧 项目特定操作

### 状态管理重构专用命令

```bash
# 快速切换到状态管理分支
alias 切换状态管理="git checkout feature/03-状态管理复杂性"

# 快速提交状态管理更改
function 提交状态管理() {
    local 描述="$1"
    git add app/shared/store/
    git commit -m "feat(state): $描述"
}

# 快速测试当前状态
function 测试当前状态() {
    echo "🧪 测试TypeScript编译..."
    npx tsc --noEmit

    echo "🚀 测试开发服务器启动..."
    timeout 30s pnpm dev > /dev/null 2>&1

    if [ $? -eq 0 ] || [ $? -eq 124 ]; then
        echo "✅ 测试通过"
    else
        echo "❌ 测试失败"
    fi
}
```

### 分支状态检查

```bash
# 检查当前分支状态
function 检查分支状态() {
    echo "📊 当前分支状态"
    echo "=================="
    echo "🌿 当前分支: $(git branch --show-current)"
    echo "📝 最后提交: $(git log --oneline -1)"
    echo "📊 未提交文件:"
    git status --porcelain
    echo "🏷️ 最近标签: $(git describe --tags --abbrev=0 2>/dev/null || echo '无标签')"
}
```

## 📊 提交信息规范

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `refactor`: 重构代码
- `docs`: 文档更新
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 提交格式

```
类型(范围): 简短描述

详细描述（可选）
- 具体变更1
- 具体变更2
```

### 示例

```bash
git commit -m "feat(state): 实现统一状态管理架构

- 创建UI和Canvas状态slice
- 添加选择器hooks
- 保持向后兼容性"
```

## ✅ 检查清单

### 每次提交前检查

- [ ] 代码编译无错误 (`npx tsc --noEmit`)
- [ ] 开发服务器可以启动 (`pnpm dev`)
- [ ] 提交信息描述清晰
- [ ] 相关文档已更新

### 分支合并前检查

- [ ] 所有功能已完成
- [ ] 所有测试通过
- [ ] 代码已经过review
- [ ] 创建了完成标签
- [ ] 更新了相关文档

### 紧急情况检查

- [ ] 确认回滚的必要性
- [ ] 备份了当前状态
- [ ] 选择了正确的回滚目标
- [ ] 通知了相关人员

这个快速操作指南涵盖了项目重构过程中的主要Git操作，你觉得还需要补充什么内容吗？
