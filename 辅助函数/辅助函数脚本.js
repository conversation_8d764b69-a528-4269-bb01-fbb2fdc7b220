// 初始化全局变量 保存监听到的数据
// 根据需求文档 使用 window.wathDat (保持原始拼写)
window.wathDat = []

// 监听滑块变化的主函数
function initSliderWatcher() {
    // 查找滑块容器 注意CSS选择器中间有空格
    const sliderContainer = document.querySelector('.slider-component.undefined-disabled.hide-rail')

    if (!sliderContainer) {
        console.warn('未找到滑块容器 将在DOM变化时重试')
        return
    }

    // 查找隐藏的input元素
    const hiddenInput = sliderContainer.querySelector('input[style*="display: none"]')

    if (!hiddenInput) {
        console.warn('未找到隐藏的input元素')
        return
    }

    // 获取drag-pad元素的HTML
    function getDragPadHTML() {
        const dragPad = document.querySelector('.drag-pad')
        if (dragPad) {
            return dragPad.outerHTML
        }
        return ''
    }

    // 保存数据到全局变量
    function saveWatchData(value, html) {
        const dataEntry = {
            value: parseInt(value),
            html: html,
            timestamp: new Date().toISOString(), // 添加时间戳便于调试
        }

        window.wathDat.push(dataEntry)
        // eslint-disable-next-line no-console
        console.log('保存数据:', dataEntry)
    }

    // 监听input值变化
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                const currentValue = hiddenInput.value
                const dragPadHTML = getDragPadHTML()

                // 保存当前状态
                saveWatchData(currentValue, dragPadHTML)
            }
        })
    })

    // 开始监听input的value属性变化
    observer.observe(hiddenInput, {
        attributes: true,
        attributeFilter: ['value'],
    })

    // 同时监听input的change和input事件 作为备用方案
    hiddenInput.addEventListener('input', function () {
        const currentValue = this.value
        const dragPadHTML = getDragPadHTML()
        saveWatchData(currentValue, dragPadHTML)
    })

    hiddenInput.addEventListener('change', function () {
        const currentValue = this.value
        const dragPadHTML = getDragPadHTML()
        saveWatchData(currentValue, dragPadHTML)
    })

    // eslint-disable-next-line no-console
    console.log('滑块监听器已初始化')

    // 保存初始状态
    const initialValue = hiddenInput.value
    const initialHTML = getDragPadHTML()
    saveWatchData(initialValue, initialHTML)
}

// DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSliderWatcher)
} else {
    // 如果DOM已经加载完成 直接初始化
    initSliderWatcher()
}

// 如果元素还没有渲染 使用MutationObserver等待
const documentObserver = new MutationObserver(function (mutations) {
    // 检查是否有我们需要的元素被添加
    const sliderExists = document.querySelector('.slider-component.undefined-disabled.hide-rail')
    if (sliderExists && window.wathDat.length === 0) {
        initSliderWatcher()
        documentObserver.disconnect() // 停止观察
    }
})

// 观察整个document的子节点变化
documentObserver.observe(document.body, {
    childList: true,
    subtree: true,
})

// 提供查看数据的辅助函数
window.getWatchData = function () {
    return window.wathDat
}

// 提供清空数据的辅助函数
window.clearWatchData = function () {
    window.watchData = []
    // eslint-disable-next-line no-console
    console.log('监听数据已清空')
}

// eslint-disable-next-line no-console
console.log('辅助函数脚本已加载')
