id1 = [
    {
        value: 78,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.28205;"></div></div></div>',
        timestamp: '2025-08-12T14:34:14.990Z',
    },
    {
        value: 78,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.28205;"></div></div></div>',
        timestamp: '2025-08-12T14:34:14.990Z',
    },
    {
        value: 78,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.28205;"></div></div></div>',
        timestamp: '2025-08-12T14:34:14.993Z',
    },
    {
        value: 78,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.28205;"></div></div></div>',
        timestamp: '2025-08-12T14:34:14.993Z',
    },
    {
        value: 79,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.26582;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.014Z',
    },
    {
        value: 79,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.26582;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.014Z',
    },
    {
        value: 79,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.26582;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.018Z',
    },
    {
        value: 79,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.26582;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.018Z',
    },
    {
        value: 80,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.25;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.028Z',
    },
    {
        value: 80,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.25;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.028Z',
    },
    {
        value: 80,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.25;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.031Z',
    },
    {
        value: 80,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.25;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.031Z',
    },
    {
        value: 81,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.23457;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.062Z',
    },
    {
        value: 81,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.23457;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.062Z',
    },
    {
        value: 81,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.23457;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.065Z',
    },
    {
        value: 81,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.23457;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.066Z',
    },
    {
        value: 82,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.21951;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.082Z',
    },
    {
        value: 82,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.21951;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.083Z',
    },
    {
        value: 82,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.21951;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.087Z',
    },
    {
        value: 82,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.21951;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.087Z',
    },
    {
        value: 83,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.20482;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.110Z',
    },
    {
        value: 83,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.20482;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.110Z',
    },
    {
        value: 83,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.20482;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.113Z',
    },
    {
        value: 83,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.20482;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.113Z',
    },
    {
        value: 84,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.19048;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.159Z',
    },
    {
        value: 84,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.19048;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.160Z',
    },
    {
        value: 84,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.19048;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.163Z',
    },
    {
        value: 84,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.19048;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.163Z',
    },
    {
        value: 85,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.17647;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.214Z',
    },
    {
        value: 85,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.17647;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.215Z',
    },
    {
        value: 85,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.17647;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.219Z',
    },
    {
        value: 85,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.17647;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.219Z',
    },
    {
        value: 86,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.16279;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.273Z',
    },
    {
        value: 86,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.16279;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.273Z',
    },
    {
        value: 86,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.16279;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.276Z',
    },
    {
        value: 86,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.16279;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.277Z',
    },
    {
        value: 87,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.14943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.305Z',
    },
    {
        value: 87,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.14943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.305Z',
    },
    {
        value: 87,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.14943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.308Z',
    },
    {
        value: 87,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.14943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.308Z',
    },
    {
        value: 88,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.13636;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.360Z',
    },
    {
        value: 88,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.13636;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.361Z',
    },
    {
        value: 88,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.13636;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.363Z',
    },
    {
        value: 88,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.13636;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.364Z',
    },
    {
        value: 89,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.1236;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.450Z',
    },
    {
        value: 89,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.1236;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.451Z',
    },
    {
        value: 89,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.1236;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.453Z',
    },
    {
        value: 89,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.1236;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.453Z',
    },
    {
        value: 90,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.11111;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.485Z',
    },
    {
        value: 90,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.11111;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.486Z',
    },
    {
        value: 90,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.11111;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.489Z',
    },
    {
        value: 90,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.11111;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.489Z',
    },
    {
        value: 91,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0989;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.507Z',
    },
    {
        value: 91,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0989;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.507Z',
    },
    {
        value: 91,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0989;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.510Z',
    },
    {
        value: 91,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0989;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.510Z',
    },
    {
        value: 92,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.08696;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.555Z',
    },
    {
        value: 92,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.08696;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.555Z',
    },
    {
        value: 92,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.08696;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.558Z',
    },
    {
        value: 92,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.08696;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.558Z',
    },
    {
        value: 93,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.07527;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.597Z',
    },
    {
        value: 93,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.07527;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.597Z',
    },
    {
        value: 93,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.07527;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.600Z',
    },
    {
        value: 93,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.07527;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.600Z',
    },
    {
        value: 94,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.06383;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.631Z',
    },
    {
        value: 94,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.06383;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.632Z',
    },
    {
        value: 94,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.06383;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.634Z',
    },
    {
        value: 94,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.06383;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.635Z',
    },
    {
        value: 95,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.05263;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.687Z',
    },
    {
        value: 95,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.05263;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.688Z',
    },
    {
        value: 95,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.05263;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.691Z',
    },
    {
        value: 95,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.05263;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.691Z',
    },
    {
        value: 96,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.04167;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.742Z',
    },
    {
        value: 96,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.04167;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.742Z',
    },
    {
        value: 96,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.04167;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.745Z',
    },
    {
        value: 96,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.04167;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.745Z',
    },
    {
        value: 97,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.03093;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.778Z',
    },
    {
        value: 97,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.03093;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.779Z',
    },
    {
        value: 97,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.03093;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.781Z',
    },
    {
        value: 97,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.03093;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.782Z',
    },
    {
        value: 98,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.02041;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.832Z',
    },
    {
        value: 98,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.02041;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.832Z',
    },
    {
        value: 98,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.02041;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.835Z',
    },
    {
        value: 98,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.02041;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.836Z',
    },
    {
        value: 99,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0101;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.875Z',
    },
    {
        value: 99,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0101;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.875Z',
    },
    {
        value: 99,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0101;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.878Z',
    },
    {
        value: 99,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1.0101;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.878Z',
    },
    {
        value: 100,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.910Z',
    },
    {
        value: 100,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.910Z',
    },
    {
        value: 100,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.913Z',
    },
    {
        value: 100,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.913Z',
    },
    {
        value: 101,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.990099;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.938Z',
    },
    {
        value: 101,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.990099;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.938Z',
    },
    {
        value: 101,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.990099;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.941Z',
    },
    {
        value: 101,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.990099;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.941Z',
    },
    {
        value: 102,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.980392;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.979Z',
    },
    {
        value: 102,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.980392;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.980Z',
    },
    {
        value: 102,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.980392;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.983Z',
    },
    {
        value: 102,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.980392;"></div></div></div>',
        timestamp: '2025-08-12T14:34:15.983Z',
    },
    {
        value: 103,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.970874;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.013Z',
    },
    {
        value: 103,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.970874;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.014Z',
    },
    {
        value: 103,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.970874;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.017Z',
    },
    {
        value: 103,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.970874;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.017Z',
    },
    {
        value: 104,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.961538;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.055Z',
    },
    {
        value: 104,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.961538;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.056Z',
    },
    {
        value: 104,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.961538;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.059Z',
    },
    {
        value: 104,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.961538;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.059Z',
    },
    {
        value: 105,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.952381;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.096Z',
    },
    {
        value: 105,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.952381;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.097Z',
    },
    {
        value: 105,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.952381;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.099Z',
    },
    {
        value: 105,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.952381;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.100Z',
    },
    {
        value: 106,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.943396;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.152Z',
    },
    {
        value: 106,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.943396;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.153Z',
    },
    {
        value: 106,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.943396;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.156Z',
    },
    {
        value: 106,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.943396;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.156Z',
    },
    {
        value: 107,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.934579;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.200Z',
    },
    {
        value: 107,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.934579;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.201Z',
    },
    {
        value: 107,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.934579;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.203Z',
    },
    {
        value: 107,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.934579;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.204Z',
    },
    {
        value: 108,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.925926;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.242Z',
    },
    {
        value: 108,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.925926;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.243Z',
    },
    {
        value: 108,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.925926;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.245Z',
    },
    {
        value: 108,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.925926;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.246Z',
    },
    {
        value: 109,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.917431;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.297Z',
    },
    {
        value: 109,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.917431;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.298Z',
    },
    {
        value: 109,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.917431;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.301Z',
    },
    {
        value: 109,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.917431;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.301Z',
    },
    {
        value: 110,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.909091;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.332Z',
    },
    {
        value: 110,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.909091;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.333Z',
    },
    {
        value: 110,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.909091;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.336Z',
    },
    {
        value: 110,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.909091;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.336Z',
    },
    {
        value: 111,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.900901;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.354Z',
    },
    {
        value: 111,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.900901;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.354Z',
    },
    {
        value: 111,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.900901;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.357Z',
    },
    {
        value: 111,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.900901;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.357Z',
    },
    {
        value: 112,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.892857;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.396Z',
    },
    {
        value: 112,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.892857;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.397Z',
    },
    {
        value: 112,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.892857;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.400Z',
    },
    {
        value: 112,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.892857;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.400Z',
    },
    {
        value: 113,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.884956;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.450Z',
    },
    {
        value: 113,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.884956;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.451Z',
    },
    {
        value: 113,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.884956;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.454Z',
    },
    {
        value: 113,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.884956;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.454Z',
    },
    {
        value: 114,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.877193;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.486Z',
    },
    {
        value: 114,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.877193;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.486Z',
    },
    {
        value: 114,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.877193;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.489Z',
    },
    {
        value: 114,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.877193;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.489Z',
    },
    {
        value: 115,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.869565;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.548Z',
    },
    {
        value: 115,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.869565;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.548Z',
    },
    {
        value: 115,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.869565;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.552Z',
    },
    {
        value: 115,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.869565;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.552Z',
    },
    {
        value: 116,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.862069;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.583Z',
    },
    {
        value: 116,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.862069;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.583Z',
    },
    {
        value: 116,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.862069;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.586Z',
    },
    {
        value: 116,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.862069;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.587Z',
    },
    {
        value: 117,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.854701;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.624Z',
    },
    {
        value: 117,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.854701;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.624Z',
    },
    {
        value: 117,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.854701;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.627Z',
    },
    {
        value: 117,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.854701;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.628Z',
    },
    {
        value: 118,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.847458;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.666Z',
    },
    {
        value: 118,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.847458;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.666Z',
    },
    {
        value: 118,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.847458;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.669Z',
    },
    {
        value: 118,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.847458;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.670Z',
    },
    {
        value: 119,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.840336;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.723Z',
    },
    {
        value: 119,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.840336;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.723Z',
    },
    {
        value: 119,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.840336;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.726Z',
    },
    {
        value: 119,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.840336;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.726Z',
    },
    {
        value: 120,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.833333;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.763Z',
    },
    {
        value: 120,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.833333;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.764Z',
    },
    {
        value: 120,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.833333;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.767Z',
    },
    {
        value: 120,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.833333;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.767Z',
    },
    {
        value: 121,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.826446;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.861Z',
    },
    {
        value: 121,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.826446;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.862Z',
    },
    {
        value: 121,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.826446;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.865Z',
    },
    {
        value: 121,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.826446;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.865Z',
    },
    {
        value: 122,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.819672;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.930Z',
    },
    {
        value: 122,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.819672;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.931Z',
    },
    {
        value: 122,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.819672;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.933Z',
    },
    {
        value: 122,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.819672;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.934Z',
    },
    {
        value: 123,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.813008;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.972Z',
    },
    {
        value: 123,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.813008;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.972Z',
    },
    {
        value: 123,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.813008;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.975Z',
    },
    {
        value: 123,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.813008;"></div></div></div>',
        timestamp: '2025-08-12T14:34:16.975Z',
    },
    {
        value: 124,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.806452;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.034Z',
    },
    {
        value: 124,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.806452;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.035Z',
    },
    {
        value: 124,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.806452;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.038Z',
    },
    {
        value: 124,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.806452;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.039Z',
    },
    {
        value: 125,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.8;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.089Z',
    },
    {
        value: 125,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.8;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.090Z',
    },
    {
        value: 125,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.8;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.093Z',
    },
    {
        value: 125,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.8;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.093Z',
    },
    {
        value: 126,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.793651;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.145Z',
    },
    {
        value: 126,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.793651;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.148Z',
    },
    {
        value: 126,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.793651;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.151Z',
    },
    {
        value: 126,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.793651;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.151Z',
    },
    {
        value: 127,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.787402;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.186Z',
    },
    {
        value: 127,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.787402;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.186Z',
    },
    {
        value: 127,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.787402;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.188Z',
    },
    {
        value: 127,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.787402;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.189Z',
    },
    {
        value: 128,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.78125;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.229Z',
    },
    {
        value: 128,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.78125;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.229Z',
    },
    {
        value: 128,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.78125;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.232Z',
    },
    {
        value: 128,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.78125;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.233Z',
    },
    {
        value: 129,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.775194;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.368Z',
    },
    {
        value: 129,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.775194;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.368Z',
    },
    {
        value: 129,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.775194;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.374Z',
    },
    {
        value: 129,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.775194;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.375Z',
    },
    {
        value: 130,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.769231;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.437Z',
    },
    {
        value: 130,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.769231;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.438Z',
    },
    {
        value: 130,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.769231;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.447Z',
    },
    {
        value: 130,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.769231;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.447Z',
    },
    {
        value: 131,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.763359;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.463Z',
    },
    {
        value: 131,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.763359;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.463Z',
    },
    {
        value: 131,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.763359;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.472Z',
    },
    {
        value: 131,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.763359;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.472Z',
    },
    {
        value: 132,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.757576;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.634Z',
    },
    {
        value: 132,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.757576;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.635Z',
    },
    {
        value: 132,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.757576;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.639Z',
    },
    {
        value: 132,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.757576;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.640Z',
    },
    {
        value: 133,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.75188;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.688Z',
    },
    {
        value: 133,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.75188;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.689Z',
    },
    {
        value: 133,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.75188;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.693Z',
    },
    {
        value: 133,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.75188;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.693Z',
    },
    {
        value: 134,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.746269;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.784Z',
    },
    {
        value: 134,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.746269;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.785Z',
    },
    {
        value: 134,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.746269;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.788Z',
    },
    {
        value: 134,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.746269;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.788Z',
    },
    {
        value: 135,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.740741;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.937Z',
    },
    {
        value: 135,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.740741;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.937Z',
    },
    {
        value: 135,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.740741;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.940Z',
    },
    {
        value: 135,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.740741;"></div></div></div>',
        timestamp: '2025-08-12T14:34:17.940Z',
    },
    {
        value: 136,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.735294;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.034Z',
    },
    {
        value: 136,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.735294;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.035Z',
    },
    {
        value: 136,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.735294;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.037Z',
    },
    {
        value: 136,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.735294;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.038Z',
    },
    {
        value: 137,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.729927;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.117Z',
    },
    {
        value: 137,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.729927;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.118Z',
    },
    {
        value: 137,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.729927;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.120Z',
    },
    {
        value: 137,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.729927;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.121Z',
    },
    {
        value: 138,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.724638;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.624Z',
    },
    {
        value: 138,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.724638;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.624Z',
    },
    {
        value: 138,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.724638;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.627Z',
    },
    {
        value: 138,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.724638;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.627Z',
    },
    {
        value: 139,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.719424;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.673Z',
    },
    {
        value: 139,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.719424;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.674Z',
    },
    {
        value: 139,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.719424;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.677Z',
    },
    {
        value: 139,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.719424;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.677Z',
    },
    {
        value: 140,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.714286;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.728Z',
    },
    {
        value: 140,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.714286;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.729Z',
    },
    {
        value: 140,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.714286;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.731Z',
    },
    {
        value: 140,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.714286;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.731Z',
    },
    {
        value: 141,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.70922;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.763Z',
    },
    {
        value: 141,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.70922;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.764Z',
    },
    {
        value: 141,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.70922;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.766Z',
    },
    {
        value: 141,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.70922;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.766Z',
    },
    {
        value: 142,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.704225;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.820Z',
    },
    {
        value: 142,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.704225;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.820Z',
    },
    {
        value: 142,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.704225;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.823Z',
    },
    {
        value: 142,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.704225;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.823Z',
    },
    {
        value: 143,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.699301;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.874Z',
    },
    {
        value: 143,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.699301;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.874Z',
    },
    {
        value: 143,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.699301;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.876Z',
    },
    {
        value: 143,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.699301;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.876Z',
    },
    {
        value: 144,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.694444;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.958Z',
    },
    {
        value: 144,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.694444;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.959Z',
    },
    {
        value: 144,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.694444;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.962Z',
    },
    {
        value: 144,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.694444;"></div></div></div>',
        timestamp: '2025-08-12T14:34:18.962Z',
    },
    {
        value: 145,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.689655;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.048Z',
    },
    {
        value: 145,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.689655;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.049Z',
    },
    {
        value: 145,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.689655;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.052Z',
    },
    {
        value: 145,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.689655;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.052Z',
    },
    {
        value: 146,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.684932;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.131Z',
    },
    {
        value: 146,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.684932;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.132Z',
    },
    {
        value: 146,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.684932;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.135Z',
    },
    {
        value: 146,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.684932;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.135Z',
    },
    {
        value: 147,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.680272;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.228Z',
    },
    {
        value: 147,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.680272;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.229Z',
    },
    {
        value: 147,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.680272;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.232Z',
    },
    {
        value: 147,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.680272;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.233Z',
    },
    {
        value: 148,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.675676;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.257Z',
    },
    {
        value: 148,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.675676;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.257Z',
    },
    {
        value: 148,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.675676;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.261Z',
    },
    {
        value: 148,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.675676;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.261Z',
    },
    {
        value: 149,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.671141;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.316Z',
    },
    {
        value: 149,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.671141;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.316Z',
    },
    {
        value: 149,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.671141;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.320Z',
    },
    {
        value: 149,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.671141;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.321Z',
    },
    {
        value: 150,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.666667;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.368Z',
    },
    {
        value: 150,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.666667;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.369Z',
    },
    {
        value: 150,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.666667;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.372Z',
    },
    {
        value: 150,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.666667;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.373Z',
    },
    {
        value: 151,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.662252;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.422Z',
    },
    {
        value: 151,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.662252;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.423Z',
    },
    {
        value: 151,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.662252;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.426Z',
    },
    {
        value: 151,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.662252;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.426Z',
    },
    {
        value: 152,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.657895;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.506Z',
    },
    {
        value: 152,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.657895;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.507Z',
    },
    {
        value: 152,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.657895;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.510Z',
    },
    {
        value: 152,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.657895;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.510Z',
    },
    {
        value: 153,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.653595;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.562Z',
    },
    {
        value: 153,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.653595;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.562Z',
    },
    {
        value: 153,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.653595;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.564Z',
    },
    {
        value: 153,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.653595;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.564Z',
    },
    {
        value: 154,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.649351;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.616Z',
    },
    {
        value: 154,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.649351;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.617Z',
    },
    {
        value: 154,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.649351;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.619Z',
    },
    {
        value: 154,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.649351;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.619Z',
    },
    {
        value: 155,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.645161;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.677Z',
    },
    {
        value: 155,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.645161;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.678Z',
    },
    {
        value: 155,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.645161;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.681Z',
    },
    {
        value: 155,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.645161;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.681Z',
    },
    {
        value: 156,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.641026;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.735Z',
    },
    {
        value: 156,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.641026;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.735Z',
    },
    {
        value: 156,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.641026;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.737Z',
    },
    {
        value: 156,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.641026;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.737Z',
    },
    {
        value: 157,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.636943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.771Z',
    },
    {
        value: 157,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.636943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.771Z',
    },
    {
        value: 157,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.636943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.774Z',
    },
    {
        value: 157,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.636943;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.774Z',
    },
    {
        value: 158,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.632911;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.825Z',
    },
    {
        value: 158,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.632911;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.826Z',
    },
    {
        value: 158,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.632911;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.828Z',
    },
    {
        value: 158,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.632911;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.828Z',
    },
    {
        value: 159,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.628931;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.882Z',
    },
    {
        value: 159,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.628931;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.882Z',
    },
    {
        value: 159,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.628931;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.885Z',
    },
    {
        value: 159,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.628931;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.885Z',
    },
    {
        value: 160,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.625;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.922Z',
    },
    {
        value: 160,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.625;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.922Z',
    },
    {
        value: 160,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.625;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.925Z',
    },
    {
        value: 160,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.625;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.925Z',
    },
    {
        value: 161,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.621118;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.936Z',
    },
    {
        value: 161,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.621118;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.937Z',
    },
    {
        value: 161,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.621118;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.939Z',
    },
    {
        value: 161,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.621118;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.939Z',
    },
    {
        value: 162,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.617284;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.964Z',
    },
    {
        value: 162,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.617284;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.964Z',
    },
    {
        value: 162,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.617284;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.966Z',
    },
    {
        value: 162,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.617284;"></div></div></div>',
        timestamp: '2025-08-12T14:34:19.967Z',
    },
    {
        value: 163,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.613497;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.013Z',
    },
    {
        value: 163,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.613497;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.014Z',
    },
    {
        value: 163,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.613497;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.016Z',
    },
    {
        value: 163,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.613497;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.016Z',
    },
    {
        value: 164,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.609756;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.057Z',
    },
    {
        value: 164,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.609756;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.058Z',
    },
    {
        value: 164,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.609756;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.060Z',
    },
    {
        value: 164,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.609756;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.061Z',
    },
    {
        value: 165,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.606061;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.110Z',
    },
    {
        value: 165,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.606061;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.110Z',
    },
    {
        value: 165,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.606061;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.112Z',
    },
    {
        value: 165,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.606061;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.112Z',
    },
    {
        value: 166,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.60241;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.179Z',
    },
    {
        value: 166,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.60241;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.179Z',
    },
    {
        value: 166,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.60241;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.182Z',
    },
    {
        value: 166,
        html: '<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 0.60241;"></div></div></div>',
        timestamp: '2025-08-12T14:34:20.182Z',
    },
]
