# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Claude交互规则

- 一直使用中文回复我答案

## 代码风格

使用 "if else"语法 不要使用 "swtich"语法
使用 "4个空格代码"

## Quick Start Commands

```bash
# Development
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server

# Code Quality
pnpm lint         # Run ESLint
pnpm lint:fix     # Auto-fix ESLint issues
pnpm format       # Format with Prettier
pnpm format:check # Check formatting
```

## Architecture Overview

### Core Structure

- **Next.js 15+ App Router** with TypeScript
- **Zustand** for state management (2 main stores: app state + image management)
- **Responsive design** with 800px mobile/desktop breakpoint
- **Feature-based organization** with clear separation of concerns

### Key Directories

```
app/
├── ImageMange/          # Centralized image management system
├── components/          # Reusable UI components
│   ├── CanvasContainer/ # Main canvas wrapper
│   ├── DisplayContainer/# Device layout engine
│   └── ExportPopver/    # Export functionality
├── features/            # Feature-specific modules
├── hooks/               # State management hooks
├── utils/               # Utility functions
└── config/              # Configuration files
```

### State Management

- **useAppStore** (`features/viewDimensions/utils/重构/状态管理.ts`): View dimensions, canvas sizing, export settings
- **useImageStore** (`ImageMange/imageMangeIndex.tsx`): Image storage, device-image binding, upload management
- **Custom hooks**: `useAppState.ts`, `usePasteImage.ts` for specialized state access

### Component Architecture

- **CanvasContainer**: Main responsive canvas wrapper
- **DisplayContainer**: Core rendering engine for device layouts with SVG masking
- **Layout system**: Single/Dual/Triple device configurations with 3D transforms
- **Export system**: PNG/JPEG with quality presets (1x/2x/3x scaling)

## Key Features

### Image Management

- **Advanced binding**: One-to-many image-to-device relationships
- **Smart conflict resolution**: Automatic handling of binding conflicts
- **Drag & drop + clipboard**: Full file upload with validation
- **Device uniqueness**: Each device displays one image max

### Responsive Design

- **Mobile**: Stacked layout with tab-based navigation
- **Desktop**: Three-panel layout (left sidebar, center canvas, right sidebar)
- **Keyboard shortcuts**: Q/W (tab switching), A/S/D (layout switching)

### Export System

- **Multiple formats**: PNG, JPEG
- **Quality presets**: Low/Medium/High scaling
- **Direct export**: One-click functionality
- **Clipboard integration**: Direct copy-to-clipboard

## Development Patterns

### Styling

- **Modular SCSS**: Component-scoped styles in `css/`
- **CSS custom properties**: Theme variables
- **Tailwind CSS v4**: Utility-first styling
- **Dark theme support**: Integrated throughout

### Code Standards

- **TypeScript strict mode** enabled
- **4-space indentation** (enforced by ESLint)
- **Single quotes** and **no semicolons** (Prettier + ESLint)
- **No unused variables** auto-cleanup disabled for development flexibility

### State Patterns

- **Atomic updates**: Single-source-of-truth through Zustand
- **Custom hooks**: Encapsulated state access
- **Reactive updates**: Store subscriptions for UI reactivity
- **DevTools support**: Redux DevTools integration for debugging

## Key Files to Know

- `app/page.tsx`: Main application entry point
- `app/hooks/useAppState.ts`: Core state management hooks
- `app/ImageMange/imageMangeIndex.tsx`: Image store and management
- `app/components/DisplayContainer/DisplayConfig.ts`: Layout configuration
- `app/utils/imageExport.ts`: Export functionality
- `app/features/viewDimensions/utils/重构/算法.ts`: View dimension calculations

## Development Philosophy

- 你是一位推崇代码清晰易读的程序员。请在写代码时，为所有函数和关键逻辑添加中文注释。特别是计算部分，必须用具体数值举例说明
